﻿<cfsetting enablecfoutputonly="true" />
<cfparam name="local.firstName" default="" />
<cfparam name="local.middleName" default="" />
<cfparam name="local.lastName" default="" />
<cfparam name="local.businessName" default="" />
<cfparam name="local.address1" default="" />
<cfparam name="local.address2" default="" />
<cfparam name="local.address3" default="" />
<cfparam name="local.city" default="" />
<cfparam name="local.state" default="0" />
<cfparam name="local.postalCode" default="" />
<cfparam name="local.countryID" default="" />
<cfparam name="local.email" default="" />
<cfparam name="local.homePhone" default="" />
<cfparam name="local.cellPhone" default="" />
<cfparam name="local.alternatePhone" default="" />
<cfparam name="local.homePhoneE164" default="" />
<cfparam name="local.cellPhoneE164" default="" />
<cfparam name="local.alternatePhoneE164" default="" />

<cfparam name="local.repFirstName" default="" />
<cfparam name="local.repLastName" default="" />
<cfparam name="local.relationToClient" default="" />
<cfparam name="local.repAddress1" default="" />
<cfparam name="local.repAddress2" default="" />
<cfparam name="local.repCity" default="" />
<cfparam name="local.repState" default="0" />
<cfparam name="local.repPostalCode" default="" />
<cfparam name="local.repEmail" default="" />
<cfparam name="local.repHomePhone" default="" />
<cfparam name="local.repCellPhone" default="" />
<cfparam name="local.repAlternatePhone" default="" />
<cfparam name="local.repHomePhoneE164" default="" />
<cfparam name="local.repCellPhoneE164" default="" />
<cfparam name="local.repAlternatePhoneE164" default="" />

<cfparam name="local.sourceID" default="" />
<cfparam name="local.feeTypeID" default="" />
<cfparam name="local.otherSource" default="" />
<cfparam name="local.counselorName" default="" />
<cfparam name="local.counselorNotes" default="" />
<cfparam name="local.attorneyNotes" default="" />
<cfparam name="local.typeID" default="" />
<cfparam name="local.agencyID" default="" />
<cfparam name="local.agencyName" default="" />
<cfparam name="local.communicateLanguageID" default="" />
<cfparam name="local.issueDesc" default="" />
<cfparam name="local.sendSurvey" default="0" />
<cfparam name="local.sendNewsBlog" default="0" />
<cfsetting enablecfoutputonly="false" />
<cfoutput>
	<style>
		div.card:not(:first-child){margin-top:8px;}
		##frmClient .itiWrap  label{
			font-size: 1em !important;
			top: var(--top-position) !important;
			transform: translateY(-50%) scale(.7) !important;
			visibility: visible !important;
		}
		##frmClient .iti__flag-container .iti__selected-flag{
			padding: 9px 6px 0 10px !important;
		}
	</style>
	<script src="/assets/common/javascript/intl-tel-input/25.2.0/js/intlTelInputWithUtils.min.js"></script>	
	<script language="JavaScript">
		var arrPhoneNumbersIds = ['homePhone','cellPhone','alternatePhone','repHomePhone','repCellPhone','repAlternatePhone'];
		var MFAPhNo = [];
		var MFAPhNoInput = [];
		var MFAPhNoErrMsg = '';
		var MFAPhNoValidMsg = '';
		var MFAPhNoErrMap = ["Invalid number", "Invalid country code", "Too short", "Too long", "Invalid number"];
		var MFAPhNoOTPTimer = 0;
		var #toScript(local.countryCode, "countryCode")#;
		$(document).ready(function(){	
			var #ToScript(local.clientID,"clientIDInt")#;
			var #ToScript(local.homePhone,"strHomePhone")#;
			var #ToScript(local.cellPhone,"strCellPhone")#;
			var #ToScript(local.alternatePhone,"strAlternatePhone")#;
			var #ToScript(local.homePhoneE164,"strHomePhoneE164")#;
			var #ToScript(local.cellPhoneE164,"strCellPhoneE164")#;
			var #ToScript(local.alternatePhoneE164,"strAlternatePhoneE164")#;
			var #ToScript(local.repHomePhone,"strRepHomePhone")#;
			var #ToScript(local.repCellPhone,"strRepCellPhone")#;
			var #ToScript(local.repAlternatePhone,"strRepAlternatePhone")#;
			var #ToScript(local.repHomePhoneE164,"strRepHomePhoneE164")#;
			var #ToScript(local.repCellPhoneE164,"strRepCellPhoneE164")#;
			var #ToScript(local.repAlternatePhoneE164,"strRepAlternatePhoneE164")#;				
			
			$(arrPhoneNumbersIds).each(function(key,value){
				MFAPhNoInput.push(0);
				if($("##"+value).length){
					MFAPhNo[key] = document.querySelector("##"+value);
				MFAPhNoErrMsg = $("."+value);
				MFAPhNoValidMsg = $("##MFAPhNoValidMsg"+value);
				if(countryCode == '' || countryCode == undefined){
					countryCode = 'us';
				}

				countryCode = countryCode.toLowerCase();
				strNationalMode = false;
				strNumberVal = $(MFAPhNo[key]).val().trim();	
				

				if(value == 'homePhone'){
					if(strHomePhoneE164.trim() != ''){
						strNationalMode = false;
					}else{
						strNationalMode = true;
					}
				}

				if(value == 'cellPhone'){
					if(strCellPhoneE164.trim() != ''){
						strNationalMode = false;
					}else{
						strNationalMode = true;
					}
				}

				if(value == 'alternatePhone'){
					if(strAlternatePhoneE164.trim() != ''){
						strNationalMode = false;
					}else{
						strNationalMode = true;
					}
				}

				if(value == 'repHomePhone'){
					if(strRepHomePhoneE164.trim() != ''){
						strNationalMode = false;
					}else{
						strNationalMode = true;
					}
				}

				if(value == 'repCellPhone'){
					if(strRepCellPhoneE164.trim() != ''){
						strNationalMode = false;
					}else{
						strNationalMode = true;
					}
				}

				if(value == 'repAlternatePhone'){
					if(strRepAlternatePhoneE164.trim() != ''){
						strNationalMode = false;
					}else{
						strNationalMode = true;
					}
				}

				if(strNumberVal == ''){
					strNationalMode = true;
				}
				
				if(clientIDInt == undefined || clientIDInt == 'NULL' || clientIDInt == 0){
					strNationalMode = true;
				}
				
				/* initialise plugin */
				MFAPhNoInput[key] = window.intlTelInput(MFAPhNo[key], {
					initialCountry: countryCode,
					nationalMode:strNationalMode,
					loadUtils: () => import("/assets/common/javascript/intl-tel-input/25.2.0/js/utils.js"), // leading slash (and http-server) required for this to work in chrome
				});
				var MFAPhNoReset = () => {
					MFAPhNoErrMsg.html('').hide();
					MFAPhNoValidMsg.hide();
				};
				/* on blur: validate */
				MFAPhNo[key].addEventListener('blur', () => {
					MFAPhNoReset();
					setNumberFormats(key)
				});
			
				/* on keyup / change flag: reset */
				MFAPhNo[key].addEventListener('change', MFAPhNoReset);
				MFAPhNo[key].addEventListener('keyup', MFAPhNoReset);
				}				
				
			})
			
		});
	</script>
<cfset local.labelColWidth = "col-sm-3">
<cfset local.fieldColWidth = "col-sm-7">

<cfif val(local.clientID)>
	<div class="font-weight-bold">Jump to sections of this page:</div>
	<div class="pt-1 pb-3 text-dark">
		<a href="##clientInfoSection" class="m-1 btn btn-outline-dark p-2 text-center">Client Information</a>
		<cfif val(local.clientID)>
		<a href="##clientFeeInfoSection" class="m-1 btn btn-outline-dark p-2 text-center">Client Fees Information</a>
		</cfif>
		<a href="##repSection" class="m-1 btn btn-outline-dark p-2 text-center">Representative</a>
		<a href="##sourceSection" class="m-1 btn btn-outline-dark p-2 text-center">Source</a>
		<a href="##counselorSection" class="m-1 btn btn-outline-dark p-2 text-center">Counselor</a>
		<cfif val(local.isReferred)>
		<a href="##lawyerSection" class="m-1 btn btn-outline-dark p-2 text-center">Lawyer Notes</a>
		</cfif>
		<a href="##callSection" class="m-1 btn btn-outline-dark p-2 text-center">Call</a>
		<a href="##agencySection" class="m-1 btn btn-outline-dark p-2 text-center">Agency</a>
		<a href="##languageSection" class="m-1 btn btn-outline-dark p-2 text-center">Language</a>
		<a href="##legalIssueSection" class="m-1 btn btn-outline-dark p-2 text-center">Legal Issue</a>
		<a href="##surveySection" class="m-1 btn btn-outline-dark p-2 text-center">Survey</a>
		<a href="##newsLetterSection" class="m-1 btn btn-outline-dark p-2 text-center">Newsletters/Blogs</a>
		<cfif NOT val(local.caseID)>
		<a href="##refStatusSection" class="m-1 btn btn-outline-dark p-2 text-center">Referral Status</a>
		</cfif>
		<cfif val(local.caseID) AND len(local.qryGetReferralFeeTypes.allowFeeTypeMgmt) AND local.qryGetReferralFeeTypes.allowFeeTypeMgmt>
		<a href="##feeTypeSection" class="m-1 btn btn-outline-dark p-2 text-center">Fee Type</a>
		<cfelseif val(local.caseID)>
		<a href="##feesInfo" class="m-1 btn btn-outline-dark p-2 text-center">Case Information</a>
		</cfif>
		<cfif val(local.isReferred)>
		<a href="##filtersSection" class="m-1 btn btn-outline-dark p-2 text-center">Attorney Details</a>
		<a href="##attorneyFieldsSection" class="m-1 btn btn-outline-dark p-2 text-center">Attorney Fields</a>
		</cfif>
		<cfif len(local.questionAnswerPath)>
		<a href="##questionSection" class="m-1 btn btn-outline-dark p-2 text-center">Question/Answer Path</a>
		</cfif>
		<cfif local.extraInformation.hasFields>
		<a href="##extraInfoSection" class="m-1 btn btn-outline-dark p-2 text-center">Extra Information</a>
		</cfif>
		<cfif val(local.clientReferralID)>
		<a href="##refLinksSection" class="m-1 btn btn-outline-dark p-2 text-center">Referral Links</a>
		</cfif>
		<a href="##docSection" class="m-1 btn btn-outline-dark p-2 text-center">Documents</a>
		<cfif val(local.clientReferralID)>
			<a href="##refHistorySection" class="m-1 btn btn-outline-dark p-2 text-center">Referral History</a>
		</cfif>
	</div>
</cfif>
<div class="alert alert-danger alert-dismissible fade show hidden" role="alert" id="clientMsgWrapper">
	<span id="clientMsg"></span>
	<button type="button" class="close" aria-label="Close" data-hide="alert">
		<span aria-hidden="true">&times;</span>
	</button>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="changePanelWarning" role="alert"></div>
	</div>
</div>

<a name="clientInfoSection" class="mc_anchor"></a>
<div class="card card-box">
	<div class="card-header bg-secondary">
		<div class="card-header--title font-weight-bold">Client Information</div>
		<cfif val(local.clientID)>
			<cfset local.refInfoWidth = len(local.dateLastUpdated) ? 520 : 300>
			<div class="card-header--actions" style="width:#local.refInfoWidth#px;">
				<div id="refDatesInfoController" class="text-right">
					<a href="##" onclick="toggleRefDatesInfo();return false;"><i class="fa-solid fa-circle-info"></i> See More Referral Information</a>
				</div>
				<div id="refDatesInfoContainer" style="display:none;">
					<div class="d-flex">
						<div class="col px-0"><span class="font-size-xs text-dim">Call Date:</span><br/>#DateTimeFormat(local.callDate,"m/d/yyyy h:nn tt")#</div>
						<cfif len(local.dateLastUpdated)>
							<div class="col px-0"><span class="font-size-xs text-dim">Last Updated Date:</span><br/>#DateTimeFormat(local.dateLastUpdated,"m/d/yyyy h:nn tt")#</div>
						</cfif>
						<div class="col px-0"><span class="font-size-xs text-dim">Referral Date:</span><br/>#DateTimeFormat(local.clientReferralDate,"m/d/yyyy h:nn tt")#</div>
					</div>
					<cfif val(local.caseID)>
						<div class="border my-1"></div>
						<div class="d-flex">
							<div class="col px-0"><span class="font-size-xs text-dim">Case Open Date:</span><br/>#DateTimeFormat(local.dateCaseOpened,"m/d/yyyy h:nn tt")#</div>
							<div class="col px-0"><span class="font-size-xs text-dim">Case Closed Date:</span><br/>#DateTimeFormat(local.dateCaseClosed,"m/d/yyyy h:nn tt")#</div>
							<div class="col px-0 font-size-xs align-self-center text-muted">
								All times listed in Central Time
								<a href="##" onclick="toggleRefDatesInfo();return false;"><i class="fa-solid fa-circle-info"></i> Hide Info</a>
							</div>
						</div>
					<cfelse>
						<div class="text-center font-size-xs text-muted">
							All times listed in Central Time&nbsp;&nbsp;&nbsp;
							<a href="##" onclick="toggleRefDatesInfo();return false;"><i class="fa-solid fa-circle-info"></i> Hide Info</a>
						</div>
					</cfif>
				</div>
			</div>
		</cfif>
	</div>
	<div class="card-body">
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="firstName" id="firstName" class="form-control" value="#local.firstName#" autocomplete="off" maxlength="75">
					<label for="firstName">First Name <span class="text-danger">*</span></label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="middleName" id="middleName" class="form-control" value="#local.middleName#" autocomplete="off" maxlength="25">
					<label for="middleName">Middle Name</label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="lastName" id="lastName" class="form-control" value="#local.lastName#" autocomplete="off" maxlength="75">
					<label for="lastName">Last Name <span class="text-danger">*</span></label>
				</div>
			</div>
		</div>
		<div class="form-label-group">
			<input type="text" name="businessName" id="businessName" class="form-control" value="#local.businessName#" autocomplete="off" maxlength="100">
			<label for="businessName">Business</label>
		</div>

		<div class="form-label-group">
			<input type="text" name="address1" id="address1" class="form-control" value="#local.address1#" autocomplete="off" maxlength="100">
			<label for="address1">Address 1</label>
		</div>
		<div class="form-label-group">
			<input type="text" name="address2" id="address2" class="form-control" value="#local.address2#" autocomplete="off" maxlength="100">
			<label for="address2">Address 2</label>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="city" id="city" class="form-control" value="#local.city#" autocomplete="off" maxlength="100">
					<label for="city">City</label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<select name="state" id="state" class="form-control">
						<option value=""></option>
						</cfoutput>
						<cfoutput query="local.qryStates" group="countryID">
						<optgroup label="#local.qryStates.country#">
						<cfoutput>
							<option value="#local.qryStates.stateid#" <cfif local.qryStates.stateid eq local.state>selected</cfif>>#local.qryStates.stateName#</option>
						</cfoutput>
						</optgroup>
						</cfoutput>
						<cfoutput>
					</select>
					<label for="state">State</label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="postalCode" id="postalCode" class="form-control" value="#local.postalCode#" autocomplete="off" maxlength="25">
					<label for="postalCode">Zip Code</label>
				</div>
			</div>
		</div>
		<div class="form-label-group">
			<input type="text" name="email" id="email" class="form-control" value="#local.email#" autocomplete="off" maxlength="255">
			<label for="email">Email</label>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group itiWrap">
					<input type="tel" name="homePhone" id="homePhone" class="form-control" value="<cfif len(trim(local.homePhoneE164))>#trim(local.homePhoneE164)#<cfelse>#trim(local.homePhone)#</cfif>" autocomplete="off" maxlength="40">
					<input type="hidden" name="homePhoneE164" id="homePhoneE164" value="#local.homePhoneE164#">
					<input type="hidden" name="homePhoneNational" id="homePhoneNational" value="#local.homePhone#">
					<label for="homePhone">Home Phone ##</label>
				</div>
				<span class="text-danger homePhoneMsg d-none"></span>
			</div>
			<div class="col">
				<div class="form-label-group itiWrap">					
					<input type="tel" name="cellPhone" id="cellPhone" class="form-control"  value="<cfif len(trim(local.cellPhoneE164))>#trim(local.cellPhoneE164)#<cfelse>#trim(local.cellPhone)#</cfif>" autocomplete="off" maxlength="40">
					<input type="hidden" name="cellPhoneE164" id="cellPhoneE164" value="#local.cellPhoneE164#">
					<input type="hidden" name="cellPhoneNational" id="cellPhoneNational" value="#local.cellPhone#">
					<label for="cellPhone">Cell Phone ##</label>
				</div>
				<span class="text-danger cellPhoneMsg d-none"></span>
			</div>
			<div class="col">
				<div class="form-label-group itiWrap">
					<input type="tel" name="alternatePhone" id="alternatePhone" class="form-control"    value="<cfif len(trim(local.alternatePhoneE164))>#trim(local.alternatePhoneE164)#<cfelse>#trim(local.alternatePhone)#</cfif>" autocomplete="off" maxlength="40">
					<input type="hidden" name="alternatePhoneE164" id="alternatePhoneE164" value="#local.alternatePhoneE164#">
					<input type="hidden" name="alternatePhoneNational" id="alternatePhoneNational" value="#local.alternatePhone#">
					<label for="alternatePhone">Alternate Phone ##</label>
				</div>
				<span class="text-danger alternatePhoneMsg d-none"></span>
				<div class="form-text text-dim small reqPhoneText">At least one phone number or a valid email is required.</div>
				
			</div>
		</div>
	</div>
</div>

<cfif val(local.clientID)>
	<cfset local.qryGetPaymentDataForThisReferral = this.objAdminReferrals.getClientPaymentDataForThisReferral(orgID=arguments.event.getValue('mc_siteinfo.orgID'), clientID=local.clientID, clientReferralID=val(local.clientReferralID))>
	<cfset local.qryGetPaymentDataForThisReferralTotals = this.objAdminReferrals.getClientFeesTotals(qryItems=local.qryGetPaymentDataForThisReferral)>
	<a name="clientFeeInfoSection" class="mc_anchor"></a>
	<div class="card">
		<h6 class="card-header bg-secondary font-weight-bold">Client Fees Information</h6>
		<div class="card-body">
			<a name="clientFeesInfo" id="clientFeesInfo"></a>
			<table id="clientFeesDueTbl" width="90%" cellspacing="3" class="table table-bordered">
				<thead>
					<tr> 
						<th width="15%">Date</th> 
						<th width="23%" class="text-right">Client Fees</th> 
						<th width="23%" class="text-right">Paid to Date</th> 
						<th width="24%" class="text-right">Amount to be Paid</th> 
						<th width="15%"></th>
					</tr>
				</thead>
				<tbody>
					<cfif local.qryGetPaymentDataForThisReferral.recordCount OR local.hasConsultationFees>
						<cfif local.qryGetPaymentDataForThisReferral.recordCount>
							<cfloop query="local.qryGetPaymentDataForThisReferral">				
								<tr class="<cfif local.qryGetPaymentDataForThisReferral.currentrow mod 2 eq 0>ev_modern<cfelse>odd_modern</cfif>">
									<td>#dateFormat(local.qryGetPaymentDataForThisReferral.transactionDate,"mm/dd/yyyy")#</td> 
									<td align="right">#numberFormat(local.qryGetPaymentDataForThisReferral.clientReferralDues,"_$_9,999.99")#</td>
									<td align="right">#numberFormat(local.qryGetPaymentDataForThisReferral.paidToDate,"_$_9,999.99")#</td> 
									<td align="right">#numberFormat(local.qryGetPaymentDataForThisReferral.amtToBePaid,"_$_9,999.99")#</td> 
									<td align="center">&nbsp;</td>
								</tr> 
							</cfloop>
							<tr>
								<td class="totals">TOTALS</td> 
								<td align="right" class="totals">#numberFormat(local.qryGetPaymentDataForThisReferralTotals.clientReferralDuesTotal,"_$_9,999.99")#</td>
								<td align="right" class="totals">#numberFormat(local.qryGetPaymentDataForThisReferralTotals.paidToDateTotal,"_$_9,999.99")#</td>
								<td align="right" class="totals">#numberFormat(local.qryGetPaymentDataForThisReferralTotals.amtToBePaidTotal,"_$_9,999.99")#</td> 
								<td align="center" class="totals">
									<cfif val(local.qryGetPaymentDataForThisReferralTotals.amtToBePaidTotal)>
										<button type="button" id="clientPaymentBtn" class="clientPaymentBtn btn btn-sm btn-secondary"><i class="fa-solid fa-money-bill-alt text-green"></i> &nbsp;Pay Fee</button>
									</cfif>
								</td>
							</tr>
						</cfif>
						<cfif local.hasConsultationFees>
							<cfloop query="local.qryConsultationFees">				
								<tr class="<cfif local.qryConsultationFees.currentrow mod 2 eq 0>ev_modern<cfelse>odd_modern</cfif>">
									<td>#dateFormat(local.qryConsultationFees.transactionDate,"m/d/yyyy")#</td> 
									<td class="text-right">#dollarFormat(local.qryConsultationFees.referralDues)#</td>
									<td class="text-right">#dollarFormat(local.qryConsultationFees.paidToDate)#</td> 
									<td class="text-right">#dollarFormat(local.qryConsultationFees.amtToBePaid)#</td> 
									<td class="text-center">
										<cfif val(local.qryConsultationFeeTotals.amtToBePaidTotal)>
											<cfset local.thisConsultationFeePmtEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryGetClientData.memberID, t=local.qryConsultationFees.detail, ta=local.qryConsultationFees.amtToBePaid, tmid=local.qryGetClientData.memberID, ad="s|#local.qryConsultationFees.saleTID#")>
											<button type="button" class="btn btn-sm btn-secondary" onclick="addPayment('#local.thisConsultationFeePmtEncString#');"><i class="fa-solid fa-money-bill-alt text-green"></i> Pay Fee</button>
										</cfif>
									</td>
								</tr> 
							</cfloop>
							<tr class="font-weight-bold">
								<td>TOTALS</td>
								<td class="text-right">#dollarFormat(local.qryConsultationFeeTotals.referralDuesTotal)#</td>
								<td class="text-right">#dollarFormat(local.qryConsultationFeeTotals.paidToDateTotal)#</td>
								<td class="text-right">#dollarFormat(local.qryConsultationFeeTotals.amtToBePaidTotal)#</td> 
								<td></td>
							</tr>
						</cfif>
					<cfelse>
						<tr>
							<td colspan="5" class="text-center"><i>No transaction activity found.</i></td>
						</tr> 
					</cfif>
				</tbody>									
			</table>		
		</div>
	</div>
</cfif>

<cfset local.labelColWidth = "col-sm-3 col-xl-2">
<cfset local.fieldColWidth = "col-sm-7 col-xl-4">
<a name="repSection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold">Representative</h6>
	<div class="card-body">
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="repFirstName" id="repFirstName" class="form-control" value="#local.repFirstName#" autocomplete="off" maxlength="75">
					<label for="repFirstName">First Name</label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="repLastName" id="repLastName" class="form-control" value="#local.repLastName#" autocomplete="off" maxlength="75">
					<label for="repLastName">Last Name</label>
				</div>
			</div>
		</div>
		<div class="form-label-group">
			<input type="text" name="relationToClient" id="relationToClient" class="form-control" value="#local.relationToClient#" autocomplete="off" maxlength="100">
			<label for="relationToClient">Relationship to Client</label>
		</div>
		<div class="form-label-group">
			<input type="text" name="repAddress1" id="repAddress1" class="form-control" value="#local.repAddress1#" autocomplete="off" maxlength="100">
			<label for="repAddress1">Address 1</label>
		</div>
		<div class="form-label-group">
			<input type="text" name="repAddress2" id="repAddress2" class="form-control" value="#local.repAddress2#" autocomplete="off" maxlength="100">
			<label for="repAddress2">Address 2</label>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="repCity" id="repCity" class="form-control" value="#local.repCity#" autocomplete="off" maxlength="100">
					<label for="repCity">City</label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<select name="repState" id="repState" class="form-control">
						<option value=""></option>
						</cfoutput>
						<cfoutput query="local.qryStates" group="countryID">
						<optgroup label="#local.qryStates.country#">
						<cfoutput>
							<option value="#local.qryStates.stateid#" <cfif local.qryStates.stateid eq local.repState>selected</cfif>>#local.qryStates.stateName#</option>
						</cfoutput>
						</optgroup>
						</cfoutput>
						<cfoutput>
					</select>
					<label for="repState">State</label>
				</div>
			</div>
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="repPostalCode" id="repPostalCode" class="form-control" value="#local.repPostalCode#" autocomplete="off" maxlength="25">
					<label for="repPostalCode">Zip Code</label>
				</div>
			</div>
		</div>
		<div class="form-label-group">
			<input type="text" name="repEmail" id="repEmail" class="form-control" value="#local.repEmail#" autocomplete="off"  maxlength="255">
			<label for="repEmail">E-mail</label>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group itiWrap">
					<input type="tel" name="repHomePhone" id="repHomePhone" class="form-control" data-enablenat="<cfif len(trim(local.repHomePhoneE164))>0<cfelse>1</cfif>" value="<cfif len(trim(local.repHomePhoneE164))>#trim(local.repHomePhoneE164)#<cfelse>#trim(local.repHomePhone)#</cfif>" autocomplete="off" maxlength="40">
					<input type="hidden" name="repHomePhoneE164" id="repHomePhoneE164" value="#local.repHomePhoneE164#">
					<input type="hidden" name="repHomePhoneNational" id="repHomePhoneNational" value="#local.repHomePhone#">
					<label for="repHomePhone">Home Phone ##</label>
				</div>
				<span class="text-danger repHomePhoneMsg d-none"></span>
			</div>
			<div class="col">
				<div class="form-label-group itiWrap">
					<input type="tel" name="repCellPhone" id="repCellPhone" class="form-control" data-enablenat="<cfif len(trim(local.repCellPhoneE164))>0<cfelse>1</cfif>" value="<cfif len(trim(local.repCellPhoneE164))>#trim(local.repCellPhoneE164)#<cfelse>#trim(local.repCellPhone)#</cfif>" autocomplete="off" maxlength="40">
					<input type="hidden" name="repCellPhoneE164" id="repCellPhoneE164" value="#local.repCellPhoneE164#">
					<input type="hidden" name="repCellPhoneNational" id="repCellPhoneNational" value="#local.repCellPhone#">
					<label for="repCellPhone">Cell Phone ##</label>
				</div>
				<span class="text-danger repCellPhoneMsg d-none"></span>
			</div>
			<div class="col">
				<div class="form-label-group itiWrap">
					<input type="tel" name="repAlternatePhone" id="repAlternatePhone" class="form-control" data-enablenat="<cfif len(trim(local.repAlternatePhoneE164))>0<cfelse>1</cfif>" value="<cfif len(trim(local.repAlternatePhoneE164))>#trim(local.repAlternatePhoneE164)#<cfelse>#trim(local.repAlternatePhone)#</cfif>" autocomplete="off" maxlength="40">
					<input type="hidden" name="repAlternatePhoneE164" id="repAlternatePhoneE164" value="#local.repAlternatePhoneE164#">
					<input type="hidden" name="repAlternatePhoneNational" id="repAlternatePhoneNational" value="#local.repAlternatePhone#">
					<label for="repAlternatePhone">Alternate Code</label>
				</div>
				<span class="text-danger repAlternatePhoneMsg d-none"></span>
				<div class="form-text text-dim small reqPhoneText">At least one phone number or a valid email is required if Representative information is provided.</div>
			</div>
		</div>		
	</div>
</div>
<a name="sourceSection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold">Source</h6>
	<div class="card-body">
		<div class="form-group row">
			<label for="sourceID" class="#local.labelColWidth# col-form-label">Source&nbsp;*</label>
			<div class="#local.fieldColWidth#">
				<div class="input-group input-group-sm">
					<select name="sourceID" id="sourceID" class="form-control form-control-sm">
						<option value=""></option>
						<cfloop query="local.qryGetReferralSources">
							<option value="#local.qryGetReferralSources.clientReferralSourceID#" <cfif (val(local.sourceID ) and local.qryGetReferralSources.clientReferralSourceID eq local.sourceID  and not arguments.event.valueExists('copy')) OR (not val(local.sourceID) and val(local.qryGetReferralSources.isDefaultSource)) OR (arguments.event.valueExists('copy') and val(local.qryGetReferralSources.isCopyDefault))>selected</cfif>>#local.qryGetReferralSources.clientReferralSource#</option>
						</cfloop>
					</select>
					<cfinput name="otherSource"  id="otherSource" type="text" size="30" maxlength="100" value="#local.otherSource#"/>
				</div>
			</div>
		</div>
	</div>
</div>

<cfset local.labelColWidth = "col-sm-3 col-xl-2">
<cfset local.fieldColWidth = "col-sm-9 col-xl-8">
<a name="counselorSection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold">Counselor</h6>
	<div class="card-body">
		<div class="form-group row">
			<label  class="#local.labelColWidth#">Counselor Name</label>
			<div class="#local.fieldColWidth#">
				<span id="counselorNameHolder">#local.counselorName#</span>
				<cfif val(local.clientReferralID)>
					<cfif val(local.counselorSelectGroupID) AND local.isCounselorReassignRights>
						<button type="button" class="btn btn-sm btn-primary ml-3" id="reAssignCounselorBtn" onclick="fnReAssignCounselor();">Reassign</button>
						<div class="col-6 mt-2 pl-0" id="divCounselorListWrapper"></div>
					<cfelseif local.isSiteAdminRights AND (local.enteredByMemberID NEQ session.cfcuser.memberdata.memberID)>
						<button type="button" class="btn btn-sm btn-primary ml-3" id="assignToMyselfBtn" onclick="fnAssignToMyself();">Assign to Me</button>
					</cfif>
				</cfif>
			</div>
		</div>
		<div class="form-group row">
			<label for="counselorNotes" class="#local.labelColWidth# col-form-label" >Counselor Notes</label>
			<div class="#local.fieldColWidth#">
				<div id="viewCounselorNoteHolder" class="mb-4 mt-2<cfif NOT val(local.clientReferralID)> d-none</cfif>">
					<button type="button" class="btn btn-sm btn-primary " id="viewCounselorNotesBtn" onclick="fnShowReferralNotes(#local.clientReferralID#,'C');">View Notes</button> 
					&nbsp;<i id="pendingNoteCount"><cfif local.qryPendingCounselorNotes.recordCount>#local.qryPendingCounselorNotes.recordCount# Pending</cfif></i>
				</div>
				<button type="button" class="btn btn-sm btn-secondary addNotesBtn" data-type="C" data-cid="#val(local.clientReferralID)#">Add Note</button>
			</div>
		</div>
	</div>
</div>

<cfif val(local.isReferred)>
	<a name="lawyerSection" class="mc_anchor"></a>
	<div class="card">
		<h6 class="card-header bg-secondary font-weight-bold">Lawyer</h6>
		<div class="card-body">
			<div class="form-group row">
				<label class="#local.labelColWidth#">Lawyer Name</label>
				<div class="#local.fieldColWidth#">#local.memberName#</div>
			</div>

			<div class="form-group row">
				<label class="#local.labelColWidth#">Lawyer Notes</label>
				<div class="#local.fieldColWidth#">
					<button type="button" class="btn btn-sm btn-primary mb-4 mt-2" id="viewLawyerNotesBtn" onclick="fnShowReferralNotes(#local.clientReferralID#,'A');">View Notes</button>
					<cfinput type="hidden" name="attorneyNotes"  id="attorneyNotes" value="#local.attorneyNotes#" />
				</div>
			</div>
		</div>
	</div>
</cfif>

<cfset local.labelColWidth = "col-sm-3 col-xl-2">
<cfset local.fieldColWidth = "col-sm-7 col-xl-4">
<a name="callSection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold">Call</h6>
	<div class="card-body">
		<div class="form-group row">
			<cfif not val(local.isReferred) and not val(local.isClosed) and not val(local.isDeleted)>
				<label  for="typeID" class="#local.labelColWidth# col-form-label">Call Type&nbsp;*</label>
			<cfelse>
				<label  for="typeID" class="#local.labelColWidth#">Call Type</label>
			</cfif>
			<cfif local.typeID eq 0>
				<cfset local.typeID  = local.defaultCallType>
			</cfif>
			<div class="#local.fieldColWidth#">
				<cfif not val(local.isReferred) and not val(local.isClosed) and not val(local.isDeleted)>
					<div class="input-group input-group-sm">
						<select name="typeID" id="typeID" class="form-control form-control-sm">
							<option value=""></option>
							<cfloop query="local.qryGetReferralTypes">
								<option value="#local.qryGetReferralTypes.clientReferralTypeID#" <cfif local.qryGetReferralTypes.clientReferralTypeID eq local.typeID or (local.qryGetReferralTypes.recordCount EQ 1 AND val(local.typeID) EQ "0")>selected</cfif>>#local.qryGetReferralTypes.clientReferralType#</option>
							</cfloop>
						</select>
					</div>
				<cfelse>		
					#local.clientReferralType# <cfif local.isAgencyReferral><b>#local.agencyName#</b></cfif>
					<cfinput type="hidden" name="typeID"  id="typeID" value="#local.typeID#" />
				</cfif>
			</div>
		</div>
		<cfif val(local.clientID)>	
			<div class="form-group row">
				<label  class="#local.labelColWidth#">Call ID</label>
				<div class="#local.fieldColWidth#">#local.callUID#</div>
			</div>
		</cfif>
	</div>
</div>
<a name="agencySection" class="mc_anchor"></a>
<div class="card" id="agencyFields">
	<h6 class="card-header bg-secondary font-weight-bold">Agency</h6>
	<div class="card-body">
		<div class="form-group row">
			<label  for="agencyID" class="#local.labelColWidth# col-form-label" >Agency Name</label>
			<div class="#local.fieldColWidth#">
				<div class="input-group input-group-sm">
					<select name="agencyID" id="agencyID" class="form-control form-control-sm mb-2" onchange="dspAgencyData(this.value);">
						<option value=""></option>
						<cfloop query="local.qryGetAgencies">
							<option value="#local.qryGetAgencies.agencyID#" title="#local.qryGetAgencies.agencyName#" <cfif local.qryGetAgencies.agencyID eq local.agencyID>selected</cfif>>#local.qryGetAgencies.agencyName#</option>
						</cfloop>
					</select>
				</div>
				<label for="otherAgency">Other Agency</label>
				<cfinput name="otherAgency"  id="otherAgency" type="text" size="30" maxlength="100" class="form-control form-control-sm"/>
			</div>
		</div>
		<cfif val(local.emailAgencyInfoToClient) and not val(local.isReferred) and not val(local.isClosed) and not val(local.isDeleted)>
			<div class="form-group row">
				<label for="sendAgencyInfo" class="#local.labelColWidth# col-form-label" >Send Agency Information to Client? </label>
				<div class="#local.fieldColWidth#">
					<div class="input-group input-group-sm">
						<input type="checkbox" name="sendAgencyInfo" id="sendAgencyInfo" value="1"> <label for="sendAgencyInfo"> Yes</label>
					</div>
				</div>
			</div>
		</cfif>
		<div id="agencyData"></div>	
	</div>
</div>

<cfif local.qryGetLanguages.recordCount GT 1>
	<cfset local.labelColWidth = "col-sm-6 col-xl-3">
	<cfset local.fieldColWidth = "col-sm-4 col-xl-4">
	<a name="languageSection" class="mc_anchor"></a>
	<div class="card">
		<h6 class="card-header bg-secondary font-weight-bold">Language</h6>
		<div class="card-body">
			<div class="form-group row">
				<label  for="communicateLanguageID" class="#local.labelColWidth# col-form-label" >What is your preferred language?</label>
				<div class="#local.fieldColWidth# col-form-label">
					<div class="input-group input-group-sm">
						<select name="communicateLanguageID" id="communicateLanguageID" class="form-control form-control-sm">
							<option value="">Select Language</option>
						<cfloop query="local.qryGetLanguages">
							<option value="#local.qryGetLanguages.languageID#" <cfif (local.qryGetLanguages.languageID eq local.communicateLanguageID) OR (not val(local.communicateLanguageID) and  val(local.qryGetLanguages.isDefault))>selected</cfif>>#local.qryGetLanguages.languageName#</option>
						</cfloop>
						</select>
					</div>
				</div>
			</div>
		</div>
	</div>
<cfelse>
	<input type="hidden" name="communicateLanguageID" id="communicateLanguageID" value="#local.qryGetLanguages.languageID#">
</cfif>

<cfset local.labelColWidth = "col-sm-3 col-xl-2">
<cfset local.fieldColWidth = "col-sm-9 col-xl-8">
<a name="legalIssueSection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold"> Legal Issue</h6>
	<div class="card-body">
		<div class="form-group row">
			<label for="issueDesc" class="#local.labelColWidth# col-form-label" >Description<cfif val(this.dspLegalDescription)>&nbsp;*</cfif></label>
			<div class="#local.fieldColWidth#">
				<div class="input-group input-group-sm">
					<textarea name="issueDesc" id="issueDesc" rows="5" class="form-control form-control-sm">#ReReplace(local.issueDesc, "<[^<|>]+?>", "","ALL")#</textarea>
				</div>
			</div>
		</div>
	</div>
</div>

<a name="surveySection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold">Survey</h6>
	<div class="card-body">
		<div class="form-group row">
			<label class="col-sm-8 col-xl-4 col-form-label">Client agrees to receive surveys regarding the case?</label>
			<div class="col-sm-2 col-xl-5 col-form-label">
				<div class="form-check">
					<input class="form-check-input" type="checkbox" name="sendSurvey" id="sendSurvey" value="1" <cfif local.sendSurvey OR ( NOT val(local.clientID) AND	val(local.defaultClientSurvey))>checked</cfif>>
					<label class="form-check-label" for="sendSurvey">Yes</label>
				</div>
			</div>
		</div>		
	</div>
</div>

<a name="newsLetterSection" class="mc_anchor"></a>
<div class="card">
	<h6 class="card-header bg-secondary font-weight-bold">Newsletters / Blogs</h6>
	<div class="card-body">
		<div class="form-group row">
			<label class="col-sm-10 col-xl-5 col-form-label">Client agrees to receive e-mails regarding Newsletters and/or Blog updates?</label>
			<div class="col-sm-2 col-xl-5 col-form-label">
				<div class="form-check">
					<input class="form-check-input" type="checkbox" name="sendNewsBlog" id="sendNewsBlog" value="1" <cfif local.sendNewsBlog>checked</cfif>>
					<label class="form-check-label" for="sendNewsBlog">Yes</label>
				</div>
			</div>
		</div>
	</div>
</div>

<cfif NOT val(local.caseID)>
	<cfset local.labelColWidth = "col-sm-2">
	<cfset local.fieldColWidth = "col-sm-6">
	<a name="refStatusSection" class="mc_anchor"></a>
	<div class="card">
		<h6 class="card-header bg-secondary font-weight-bold">Referral Status</h6>
		<div class="card-body">
			<div class="form-group row">
				<label for="statusID" class="#local.labelColWidth# <cfif not val(local.isPending) and not val(local.isClosed) and not val(local.isDeleted)>col-form-label</cfif>" >Status</label>
				<div class="#local.fieldColWidth#">
					<cfif not val(local.isPending) and not val(local.isClosed) and not val(local.isDeleted)>
						<div class="input-group input-group-sm">
							<select name="statusID" id="statusID" class="form-control form-control-sm">
							<cfloop query="local.qryGetReferralStatus">
								<cfif not local.qryGetReferralStatus.isPending AND (local.qryGetReferralStatus.clientReferralStatusID eq local.statusID AND NOT local.qryGetReferralStatus.isActive)>
									<option value="#local.qryGetReferralStatus.clientReferralStatusID#" <cfif local.qryGetReferralStatus.clientReferralStatusID eq local.statusID>selected</cfif> data-custom="#val(local.qryGetReferralStatus.isRetainedCase) and val(local.qryGetReferralStatus.isOpen)?1:0#">#local.qryGetReferralStatus.statusName#  (Inactive)</option>
								<cfelseif not local.qryGetReferralStatus.isPending AND local.qryGetReferralStatus.isActive>
									<option value="#local.qryGetReferralStatus.clientReferralStatusID#" <cfif local.qryGetReferralStatus.clientReferralStatusID eq local.statusID>selected</cfif> data-custom="#val(local.qryGetReferralStatus.isRetainedCase) and val(local.qryGetReferralStatus.isOpen)?1:0#">#local.qryGetReferralStatus.statusName#</option>
								</cfif>
							</cfloop>
							</select>
						</div>
					<cfelse>
						#local.statusName#
					</cfif>
				</div>
			</div>
		</div>
	</div>
</cfif>

<cfif val(local.caseID)>
	<cfset local.labelColWidth = "col-sm-2">
	<cfset local.fieldColWidth = "col-sm-4">
	<cfif len(local.qryGetReferralFeeTypes.allowFeeTypeMgmt) AND local.qryGetReferralFeeTypes.allowFeeTypeMgmt>
		<a name="feeTypeSection" class="mc_anchor"></a>
		<div class="card">
			<h6 class="card-header bg-secondary font-weight-bold">Fee Type</h6>
			<div class="card-body">
				<div class="form-group row">
					<label for="caseFeeTypeID" class="#local.labelColWidth# col-form-label" >Fee Type</label>
					<div class="#local.fieldColWidth#">
						<div class="input-group input-group-sm">
							<select name="caseFeeTypeID" id="caseFeeTypeID" class="form-control form-control-sm">
								<option value=""></option>
								<cfset local.selectedTypeFound = 0>
								<cfloop query="local.qryGetReferralFeeTypes">
									<option value="#local.qryGetReferralFeeTypes.feeTypeID#" <cfif local.qryGetReferralFeeTypes.feeTypeID eq local.feeTypeID>selected<cfset local.selectedTypeFound = 1><cfelseif NOT local.selectedTypeFound AND val(local.qryGetReferralFeeTypes.isDefault)>selected</cfif>>#local.qryGetReferralFeeTypes.feeTypeName#</option>
								</cfloop>
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
	</cfif>

	<a name="feesInfo" id="feesInfo" class="mc_anchor"></a>
	<cfset local.labelColWidth = "col-sm-3 col-xl-2">
	<cfset local.fieldColWidth = "col-sm-7 col-xl-4">
	<div class="card">
		<h6 class="card-header bg-secondary font-weight-bold">Case Information </h6>
		<div class="card-body">
			<div class="alert alert-success d-none" id="delImportedFeeMsg">
				Imported Fees Removed successfully.	
			</div>
			<div class="form-group row">
				<label  class="#local.labelColWidth# col-form-label" >Attorney</label>
				<div class="#local.fieldColWidth#">
					<a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editMemberLink#&memberID=#local.memberID#" target="_blank" title="#local.memberName#">#local.memberName#</a>
				</div>
			</div>

			<div class="form-group row">
				<label  for="statusID" class="#local.labelColWidth# col-form-label" >Status</label>
				<div class="#local.fieldColWidth#">
					<div class="input-group input-group-sm">
						<select name="statusID" id="statusID" class="form-control form-control-sm">
							<cfloop query="local.qryGetCaseStatuses">
								<cfif local.qryGetCaseStatuses.clientReferralStatusID eq local.statusID AND NOT local.qryGetCaseStatuses.isActive>
									<option value="#local.qryGetCaseStatuses.clientReferralStatusID#" selected data-custom="#val(local.qryGetCaseStatuses.isRetainedCase) and val(local.qryGetCaseStatuses.isOpen)?1:0#">#local.qryGetCaseStatuses.statusName# (Inactive)</option>
								<cfelseif local.qryGetCaseStatuses.isActive>
									<option value="#local.qryGetCaseStatuses.clientReferralStatusID#" <cfif local.qryGetCaseStatuses.clientReferralStatusID eq local.statusID>selected</cfif> data-custom="#val(local.qryGetCaseStatuses.isRetainedCase) and val(local.qryGetCaseStatuses.isOpen)?1:0#">#local.qryGetCaseStatuses.statusName#</option>
								</cfif>
							</cfloop>
						</select>
					</div>
				</div>
			</div>

			<div class="form-group row">
				<label  for="caseFees" class="#local.labelColWidth# col-form-label" >Fees Reported by Client</label>
				<div class="col-sm-3">
					<div class="input-group input-group-sm">
						<cfinput name="caseFees"  id="caseFees" type="text" size="15" maxlength="15" value="#trim(numberFormat(local.caseFees,"9,999.99"))#" class="form-control form-control-sm" />
					</div>
				</div>
			</div>
			<cfif len(local.dspLessFilingFeeCosts) AND local.dspLessFilingFeeCosts>
				<div class="form-group row">
					<label  for="filingFee" class="#local.labelColWidth# col-form-label" >Filing and Office Expenses</label>
					<div class="col-sm-3">
						<div class="input-group input-group-sm">
							<cfinput name="filingFee"  id="filingFee" type="text" size="15" maxlength="15" value="" class="form-control form-control-sm" />
						</div>
					</div>
				</div>
			</cfif>

			<div class="form-group row">
				<label  for="collectedFee" class="#local.labelColWidth# col-form-label" >Fee Collected from Client</label>
				<div class="#local.fieldColWidth#">
					<div class="input-group input-group-sm">
						<cfinput name="collectedFee"  id="collectedFee" type="text" size="15" maxlength="15" value="" class="form-control form-control-sm"/>
						<div class="input-group-append">
							<button type="button" id="addClientFeeBtn" class="saveClientBtn btn btn-sm btn-primary">Record Fee</button>
						</div>
					</div>
				</div>
			</div>
			
			<table id="feesDueTbl" width="90%" cellspacing="3" class="table table-bordered">	
				<thead>
					<tr> 
						<th width="15%" class="text-center">Date</th> 
						<th width="25%" align="right">Fees Collected from Client</th> 
						<th width="15%" align="right">Filing and Office Expenses</th> 
						<th width="15%" align="right">Referral Fees</th> 
						<th width="15%" align="right">Amount to be Paid</th> 
						<th width="15%"></th>
					</tr>		 
				</thead>
				<tbody>
					
					<cfif local.qryGetCaseFees.recordCount>	
						<cfquery name="local.qryDistinctCaseFees" dbtype="query">
							select collectedFeeID, collectedFeeDate, collectedFee, filingFee, sum(referralDues) as referralDues, 
								sum(amtToBePaid) as amtToBePaid, assignedToMemberID, assignedToMemberName
							from [local].qryGetCaseFees
							group by collectedFeeID, collectedFeeDate, collectedFee, filingFee, assignedToMemberID, assignedToMemberName
						</cfquery>
			
						<cfloop query="local.qryDistinctCaseFees">				
						<tr class="<cfif local.qryDistinctCaseFees.currentrow mod 2 eq 0>ev_modern<cfelse>odd_modern</cfif>">
							<td align="center">#dateFormat(local.qryDistinctCaseFees.collectedFeeDate,"mm/dd/yyyy")#</td> 
							<td align="right">#numberFormat(local.qryDistinctCaseFees.collectedFee,"_$_9,999.99")#</td>
							<td align="right">#numberFormat(local.qryDistinctCaseFees.filingFee,"_$_9,999.99")#</td>
							<td align="right">#numberFormat(local.qryDistinctCaseFees.referralDues,"_$_9,999.99")#</td> 
							<td align="right">#numberFormat(local.qryDistinctCaseFees.amtToBePaid,"_$_9,999.99")#</td> 
							<td align="center">
								<cfquery name="local.qryThisCaseFeeAllSaleIDs" dbtype="query">
									select saleTID
									from [local].qryGetCaseFees
									where collectedFeeID = #val(local.qryDistinctCaseFees.collectedFeeID)#
								</cfquery>
			
								<cfif val(local.qryDistinctCaseFees.amtToBePaid)>
									<cfquery name="local.qryThisCaseFeeSaleID" dbtype="query">
										select saleTID
										from [local].qryGetCaseFees
										where collectedFeeID = #val(local.qryDistinctCaseFees.collectedFeeID)#
										and amtToBePaid > 0
									</cfquery>
			
									<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryDistinctCaseFees.assignedToMemberID, t="Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.clientReferralID# of #local.qryDistinctCaseFees.assignedToMemberName#", ta=local.qryDistinctCaseFees.amtToBePaid, tmid=local.qryDistinctCaseFees.assignedToMemberID, ad="s|#valueList(local.qryThisCaseFeeSaleID.saleTID)#")>
									<a href="javascript:addPayment('#local.addPaymentEncString#')" title="Pay for Fees"><i class="fa-regular fa-circle-plus"></i></a>
									<a href="javascript:adjustTransaction(#local.caseID#,#local.qryDistinctCaseFees.collectedFeeID#,#local.qryDistinctCaseFees.assignedToMemberID#,#local.panelid1#,'#valueList(local.qryThisCaseFeeAllSaleIDs.saleTID)#',#local.clientID#,#local.clientParentID#)" title="Adjust Fees"><i class="fa-regular fa-money-check-dollar-pen"></i></a>
								<cfelse>
									<cfif val(local.qryGetCaseFees.importedCollectedFee)>
										<a href="javascript:deleteImportedFee(#local.caseID#,#local.qryDistinctCaseFees.collectedFeeID#)" title="Delete Imported Fees"><i class="fa-regular fa-trash"></i></a>
									</cfif>
									Paid
								</cfif>
								<cfquery name="local.qryThisCaseFeeSaleID" dbtype="query">
									select saleTID, amtToBePaid, cache_amountAfterAdjustment
									from [local].qryGetCaseFees
									where collectedFeeID = #val(local.qryDistinctCaseFees.collectedFeeID)#
									and amtToBePaid = 0
								</cfquery>					
								<cfif (not val(local.qryDistinctCaseFees.collectedfee) and not val(local.qryThisCaseFeeSaleID.amtToBePaid)) 
									or (val(local.qryThisCaseFeeSaleID.saleTID) and not val(local.qryThisCaseFeeSaleID.amtToBePaid) and  not val(local.qryThisCaseFeeSaleID.cache_amountAfterAdjustment))>
									<a href="javascript:removeFeesRow(#local.qryDistinctCaseFees.collectedFeeID#,'#valueList(local.qryThisCaseFeeSaleID.saleTID)#','0')" title="Remove Row"><i class="fa-regular fa-circle-minus text-danger"></i> </a>
								</cfif>
								<cfif val(local.qryDistinctCaseFees.collectedfee) and val(local.qryThisCaseFeeSaleID.saleTID) and  val(local.qryThisCaseFeeSaleID.cache_amountAfterAdjustment)>
									<a href="javascript:removeFeesRow(#local.qryDistinctCaseFees.collectedFeeID#,'#valueList(local.qryThisCaseFeeSaleID.saleTID)#','1')" title="Remove Row"><i class="fa-regular fa-circle-minus text-danger"></i></a>
								</cfif>					
							</td>
						</tr> 
						</cfloop>
						<tr>
							<td align="left" class="totals">TOTALS</td> 
							<td align="right" class="totals">#numberFormat(local.qryGetCaseFeesTotals.collectedFeeTotal,"_$_9,999.99")#</td>
							<td align="right" class="totals">#numberFormat(local.qryGetCaseFeesTotals.filingFeeTotal,"_$_9,999.99")#</td>
							<td align="right" class="totals">#numberFormat(local.qryGetCaseFeesTotals.referralDuesTotal,"_$_9,999.99")#</td> 
							<td align="right" class="totals">#numberFormat(local.qryGetCaseFeesTotals.amtToBePaidTotal,"_$_9,999.99")#</td> 
							<td align="center" class="totals">&nbsp;</td>
						</tr>
					<cfelse>
						<tr>
							<td colspan="5" style="padding-left:10px;"><i>No transaction activity found.</i></td>
						</tr> 
					</cfif>	
				<tbody>										
			</table>
			
			<cfif this.allowFeeDiscrepancy AND local.qryGetCaseFees.recordCount>
				<div class="form-group row mb-3">
					<label class="col-sm-3 b">Fee Discrepancies Status</label>
					<div class="col-sm-9">
						<button type="button" class="btn btn-sm btn-secondary" onclick="javascript:showStatusFields();">Change Status</button><br>
						<input type="hidden" name="oldfeeDiscrepancyStatusID" id="oldfeeDiscrepancyStatusID" value="#local.feeDiscrepancyStatusID#">
					</div>
				</div>

				<div class="form-group row statusFields d-none justify-content-end mb-3">
					<div class="card card-box mb-1 col-sm-8 mr-5 mt-3 p-0">
						<div class="card-body pb-3">
							<div class="form-row">
								<div class="col-12">
									<div class="form-label-group">
										<select name="feeDiscrepancyStatusID"  id="feeDiscrepancyStatusID" class="form-control"  />
											<option>Select Status</option>
											<cfloop query="local.qryGetFeeDiscrepancyStatuses">
												<option value="#local.qryGetFeeDiscrepancyStatuses.feeDiscrepancyStatusID#" <cfif local.qryGetFeeDiscrepancyStatuses.feeDiscrepancyStatusID EQ local.feeDiscrepancyStatusID> selected</cfif>>#local.qryGetFeeDiscrepancyStatuses.statusName#</option>
											</cfloop>
										</select>
										<label for="feeDiscrepancyStatusID">Status</label>
									</div>
									<div class="form-label-group ">
										<textarea class="form-control form-control-sm" id="feeDiscrepancyStatusNotes" name="feeDiscrepancyStatusNotes" rows="4" cols="50"></textarea>
										<label for="feeDiscrepancyStatusNotes">Status Notes</label>
									</div>
								</div>
							</div>									
						</div>
						<div class="card-footer text-right">
							<button type="button" class="btn btn-sm btn-primary saveClientBtn">Save</button>
						</div>
					</div>
				</div>

				<cfif local.qryGetFeeDiscrepancyStatusChangeLog.recordCount>
					<div class="form-group row mt-3 pr-5">
						<label class="col-sm-2 b align-self-center">Status Logs</label>
						<div class="col-sm-8 offset-2 mr-0 pr-0">
							<table id="feeDiscrepancyTbl" width="90%" cellspacing="3" class="table table-bordered">	
								<thead>
									<tr> 
										<th width="25%">Status</th> 
										<th width="15%" class="text-center" align="right">Date</th> 
										<th width="15%">Notes</th> 
										<th width="15%">Entered By</th> 
									</tr>		 
								</thead>
								<tbody>
									<cfloop query="local.qryGetFeeDiscrepancyStatusChangeLog">
										<tr>
											<td>#local.qryGetFeeDiscrepancyStatusChangeLog.statusName#</td>
											<td class="text-center">#DateFormat(local.qryGetFeeDiscrepancyStatusChangeLog.dateCreated,"MM/DD/YYYY")#</td>
											<td>#local.qryGetFeeDiscrepancyStatusChangeLog.statusNotes#</td>
											<td>#local.qryGetFeeDiscrepancyStatusChangeLog.enteredByFullName#</td>
										</tr>
									</cfloop>
								</tbody>
							</table>										
						</div>
					</div>
				</cfif>

				<div class="form-group row mt-2 text-right pr-4">
					<label class="col-sm-2"></label>
					<div class="col-sm-10 pl-0">
						<button id="btnSendFeeSbmsnEmail" type="button" name="btnSendFeeSbmsnEmail"class="btn btn-sm btn-secondary">Send Fee Submission Link to Client</button>
					</div>
				</div>
			</cfif>
			
			
			<cfif local.qryGetCaseFeeStructureLevels.recordCount gt 1 
		OR (local.qryGetCaseFeeStructureLevels.recordCount eq 1 and val(local.qryGetCaseFeeStructureLevels.cumulativeChargedAmount))
		OR (local.qryGetCaseFeeStructureLevels.recordCount eq 1 and val(local.qryGetCaseFeeStructureLevels.rangeInitialChargeFeePercent)
			and  val(local.qryGetCaseFeeStructureLevels.rangeInitialChargeFeePercent) neq val(local.qryGetCaseFeeStructureLevels.cumulativeFeePercent))>
				<div class="alert alert-info">
					<h6><b>Fees Reported by Client</b></h6>
					<p>If you need to edit an incorrect <b>Fee Collected from Client</b> (or adjust <b>Referral Fees</b>), please use the following process:</p>		
					<ol>
						<li>Click <b>Adjust Fees</b> (pencil and dollar icon).</li>
						<li>Enter <b>$0.00</b> in the <b>Adjusted Fee Collected from Client</b> box.</li>
						<li>Click <b>Save Adjustment.</b></li>
						<li>Now click the <b>Remove Row</b> (white box with red line) icon to the far right to delete the $0 items from the list from the referral record.</li>
						<li>Then add back the <b>Fees Collected from Client</b> amount(s) again in total or one at a time.</li>
						<li>If any prior payments were recorded, you will need to <b>reapply them with the Credit Balance</b> on the given attorney's record and then you can <b>apply payment for the remaining balance due</b>.</li>
					</ol>
				</div>
			</cfif>

			

		</div>
	</div>
</cfif>

<cfif val(local.isReferred)>
	<cfset local.labelColWidth = "col-sm-3 col-xl-2">
	<cfset local.fieldColWidth = "col-sm-7 col-xl-4">
	<a name="filtersSection" class="mc_anchor"></a>
	<div class="card">
		<h6 class="card-header bg-secondary font-weight-bold">Filters</h6>
        <div class="card-body col-xl-10">
            <input name="transferMemberId" type="hidden" value="0" id="transferMemberId">

			<div class="mb-1 alert alert-warning alert-dismissible d-none mb-2" role="alert" id="transferAlert">
				<span id="err_div">Attorney has been selected successfully. Please click "Save" to complete your transfer.</span>
				<button type="button" class="close" aria-label="Close" data-hide="alert">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>

            <div class="form-group row">
                <label  class="#local.labelColWidth#">Attorney Name</label>
                <div class="col-sm-8 col-xl-10">
                    <a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editMemberLink#&memberID=#local.memberID#" target="_blank" class="transferMemberIdElement">#local.memberName#</a><cfif local.isTransfer><button type="button" onclick="showMemSelector()" class="btn btn-sm btn-primary float-right">Transfer Case</button></cfif>
                </div>
            </div>
            <div class="form-group row">
                <label class="#local.labelColWidth#">Attorney Phone</label>
                <div class="#local.fieldColWidth#">
                    #local.lawyerPhone#
                </div>
            </div>
			<div class="form-group row">
                <label class="#local.labelColWidth#">Primary Panel</label>
                <div class="col-sm-9 col-xl-2">
                    <cfloop query="local.qryGetPanels">
                        <cfif local.panelid1 is local.qryGetPanels.panelID><a href="#this.link.editPanel#&panelID=#local.panelid1#" target="_blank" panelid1="#local.panelid1#">#local.qryGetPanels.name#</a><cfbreak /></cfif>
                    </cfloop>
				</div>
				<label class="col-sm-3 col-xl-2 ml-2">Sub-Panel</label>
				<div class="col-sm-8 col-xl-4">
                    <cfset local.subpanelid1 = "" />
                    <cfset local.subpanelid1List = "" />
                    <cfloop query="local.qryGetReferralFilterData">
                        <cfif listFindNoCase("subpanelid1",local.qryGetReferralFilterData.elementID)>
                            <cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
                                <cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
                                <cfif not listFindNoCase(local.subpanelid1, local.qryGetPanelInfo.name)>
                                    <cfset local.subpanelid1List = listAppend(local.subpanelid1List,local.thisPanelID) />
                                    <cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
                                </cfif>							
                            </cfloop>
                        </cfif>
                    </cfloop>			
                    <cfif len(local.subpanelid1)><span subpanelid1="#local.subpanelid1List#">#local.subpanelid1#</span><cfelse>N/A</cfif>
                </div>
            </div>
         
            <div class="form-group row">
                <label class="#local.labelColWidth#">Secondary Panel</label>
                <div class="col-sm-9 col-xl-2">
                    <cfloop query="local.qryGetPanels">
                        <cfif local.panelid2 is local.qryGetPanels.panelID><span panelid2="#local.panelid2#">#local.qryGetPanels.name#</span> <cfbreak /></cfif>
                    </cfloop>
				</div>
				<label class="col-sm-3 col-xl-2 ml-2">Sub-Panel</label>
                <div class="col-sm-8 col-xl-4">
                    <cfset local.subpanelid2 = "" />
                    <cfset local.subpanelid2List = "" />
                    <cfloop query="local.qryGetReferralFilterData">
                        <cfif listFindNoCase("subpanelid2",local.qryGetReferralFilterData.elementID)>
                            <cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
                                <cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
                                <cfif not listFindNoCase(local.subpanelid2, local.qryGetPanelInfo.name)>
                                    <cfset local.subpanelid2List = listAppend(local.subpanelid2List,local.thisPanelID) />
                                    <cfset local.subpanelid2 = listAppend(local.subpanelid2,local.qryGetPanelInfo.name) />
                                </cfif>
                            </cfloop>
                        </cfif>
                    </cfloop>			
                    <cfif len(local.subpanelid2)><span subpanelid2="#local.subpanelid2List#">#local.subpanelid2#</span><cfelse>N/A</cfif>
                </div>
            </div>

            <div class="form-group row">
                <label class="#local.labelColWidth#">Tertiary Panel</label>
                <div class="col-sm-9 col-xl-2">
                    <cfloop query="local.qryGetPanels">
                        <cfif local.panelid3 is local.qryGetPanels.panelID><span panelid3="#local.panelid3#">#local.qryGetPanels.name#</span> <cfbreak /></cfif>
                    </cfloop>
				</div>
				<label class="col-sm-3 col-xl-2 ml-2">Sub-Panel</label>
                <div class="col-sm-8 col-xl-4">
                    <cfset local.subpanelid3 = "" />
                    <cfset local.subpanelid3List = "" />
                    <cfloop query="local.qryGetReferralFilterData">
                        <cfif listFindNoCase("subpanelid3",local.qryGetReferralFilterData.elementID)>
                            <cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
                                <cfset local.qryGetPanelInfo = this.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
                                <cfif not listFindNoCase(local.subpanelid3, local.qryGetPanelInfo.name)>
                                    <cfset local.subpanelid3List = listAppend(local.subpanelid3List,local.thisPanelID) />
                                    <cfset local.subpanelid3 = listAppend(local.subpanelid3,local.qryGetPanelInfo.name) />
                                </cfif>
                            </cfloop>
                        </cfif>
                    </cfloop>			
                    <cfif len(local.subpanelid3)><span subpanelid3="#local.subpanelid3List#">#local.subpanelid3#</span><cfelse>N/A</cfif>	
                </div>
            </div>

			<div><div id="M_err_div" style="display:none;"></div></div>
			
            <cfif ArrayLen(local.xmlFields.xmlRoot.xmlChildren)>
                <cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
                    <cfset local.thisFieldValue = evaluate("local.#local.thisfield.xmlattributes.fieldCode#") />
                    
                    <div class="form-group row">
                        <label class="#local.labelColWidth#">#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#<cfif local.thisfield.xmlattributes.isRequired is 1>&nbsp;*</cfif></label>
                        <div class="#local.fieldColWidth#">
                            <cfswitch expression="#local.thisfield.xmlAttributes.displayTypeCode#">
                                <cfcase value="TEXTBOX">
                                    <cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode) or findNoCase('_proximity',local.thisfield.xmlattributes.dbField)>
                                        <cfset local.thisRadiusValue = evaluate("local.#local.thisfield.xmlattributes.fieldCode#_radius") />	
                                        Within 
                                            <cfloop list="5,10,25,50,100" index="local.thisrad">
                                                <cfoutput><cfif listFindNoCase(local.thisRadiusValue,local.thisrad)>#local.thisrad# <cfbreak /></cfif></cfoutput>
                                            </cfloop>	
                                        <cfoutput>miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# <cfif len(trim(local.thisFieldValue))>#local.thisFieldValue#<cfelse>--</cfif></cfoutput>
                                    <cfelse>
                                        <cfoutput>#local.thisFieldValue#</cfoutput>
                                    </cfif>
                                </cfcase>
                                <cfcase value="RADIO">
                                    <cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
                                        <cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
                                        <cfcase value="STRING">
                                            <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
                                        </cfcase>
                                        <cfcase value="DECIMAL2">
                                            <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
                                        </cfcase>
                                        <cfcase value="INTEGER">
                                            <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
                                        </cfcase>
                                        <cfcase value="DATE">
                                            <cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
                                        </cfcase>
                                        <cfcase value="BIT">
                                            <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
                                        </cfcase>
                                        <cfdefaultcase>
                                            <cfset local.thisOptColValue = "">
                                        </cfdefaultcase>
                                        </cfswitch>
                                        <cfoutput><cfif local.thisfield.xmlAttributes.dataTypeCode eq "BIT">#YesNoFormat(local.thisOptColValue)#<cfelse>#local.thisOptColValue#</cfif></cfoutput><br/>
                                    </cfloop>
                                </cfcase>
                                <cfcase value="SELECT,CHECKBOX">
                                    <cfif ReFindNoCase('ma_[0-9]+_stateprov',local.thisfield.xmlattributes.fieldCode)>
                                        <cfset local.qryStates = application.objMember.getStates(arguments.event.getValue('mc_siteinfo.orgID'))>
                                        <cfloop query="local.qryStates">
                                            <cfif local.qryStates.stateid eq local.thisFieldValue>#local.qryStates.stateName# <cfbreak /></cfif>
                                        </cfloop>
                                    <cfelseif listFindNoCase("m_recordtypeid,m_membertypeid,m_status", local.thisfield.xmlattributes.fieldCode)>
                                        <cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
                                            <cfoutput><cfif local.thisOpt.xmlAttributes.valueID eq local.thisFieldValue>#local.thisOpt.xmlAttributes.columnValueString# <cfbreak /></cfif></cfoutput>
                                        </cfloop>
                                    <cfelse>
                                        <cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
                                            <cfswitch expression="#local.thisfield.xmlAttributes.dataTypeCode#">
                                            <cfcase value="STRING">
                                                <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
                                                <cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueString>
                                            </cfcase>
                                            <cfcase value="DECIMAL2">
                                                <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
                                                <cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueDecimal2>
                                            </cfcase>
                                            <cfcase value="INTEGER">
                                                <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
                                                <cfset local.thisOptColDisplay = local.thisOpt.xmlAttributes.columnValueInteger>
                                            </cfcase>
                                            <cfcase value="DATE">
                                                <cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
                                                <cfset local.thisOptColDisplay = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"mm/dd/yyyy")>
                                            </cfcase>
                                            <cfcase value="BIT">
                                                <cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueBit>
                                                <cfset local.thisOptColDisplay = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
                                            </cfcase>
                                            <cfdefaultcase>
                                                <cfset local.thisOptColValue = "">
                                                <cfset local.thisOptColDisplay = "">
                                            </cfdefaultcase>
                                            </cfswitch>
                                            <cfoutput><cfif listFindNoCase(local.thisFieldValue,local.thisOptColValue)>- #local.thisOptColDisplay# <br /></cfif></cfoutput>
                                        </cfloop>
                                    </cfif>
                                </cfcase>
                                <cfcase value="DATE">
                                    <cfoutput>#dateFormat(local.thisFieldValue, "mm/dd/yyyy")# - #timeFormat(local.thisFieldValue,"h:mm tt")# Central</cfoutput>
                                </cfcase>
                            </cfswitch>
                        </div>
                    </div>
                </cfloop>	
            </cfif>

            <cfloop query="local.qryGetClassifications">
                <cfif val(local.qryGetClassifications.allowSearch)>
                    <div class="form-group row">
                        <label class="#local.labelColWidth#"><cfif len(trim(local.qryGetClassifications.name))>#local.qryGetClassifications.name#<cfelse>#local.qryGetClassifications.groupSetName#</cfif></label>
                        <div class="#local.fieldColWidth#">
                            <cfset local.qryGetGroupSetGroup = this.objAdminReferrals.getGroupSetGroup(local.qryGetClassifications.groupSetID) />
                            <cfset local.thisGroupIDValue = evaluate("local.mg_gid_#local.qryGetClassifications.groupSetID#") />
                            <cfloop query="local.qryGetGroupSetGroup">
                                <cfif listFind(local.thisGroupIDValue,local.qryGetGroupSetGroup.groupsetGroupID)>- #local.qryGetGroupSetGroup.labelOverride#<br /></cfif>
                            </cfloop>
                        </div>
                    </div>	
                </cfif>
            </cfloop>
        </div>
    </div>

	<cfif local.strAttorneyFields.hasFields>
		<a name="attorneyFieldsSection" class="mc_anchor"></a>
		<div class="card">
			<h6 class="card-header bg-secondary font-weight-bold">Attorney Fields</h6>
			<div class="card-body">
				<input type="hidden" name="hasAttorneyFields" id="hasAttorneyFields" value="1">
				#local.strAttorneyFields.HTML#
			</div>
		</div>	
	</cfif>
</cfif>

<cfif len(local.questionAnswerPath)>
	<a name="questionSection" class="mc_anchor"></a>
    <div class="card">
        <h6 class="card-header bg-secondary font-weight-bold">Question/Answer Path</h6>
        <div class="card-body">
            <div class="form-group row">
                <label class="col-sm-12" >#replace(local.questionAnswerPath,"\"," / ","ALL")#</label>
            </div>
        </div>
    </div>
</cfif>	

<cfif local.extraInformation.hasFields>
	<a name="extraInfoSection" class="mc_anchor"></a>
    <div class="card">
        <h6 class="card-header bg-secondary font-weight-bold">Extra Information</h6>
        <div class="card-body">
			<div class="form-group row">
				<div class="col-sm-12">
					<input type="hidden" name="hasClientFields" id="hasClientFields" value="1">
					#local.extraInformation.HTML#
				</div>
            </div>
        </div>
    </div>	
</cfif>

<div id="reqNote" class="col-sm-12 text-left pt-2">* indicates a required field</div>

<cfif val(local.clientReferralID)>
<a name="refLinksSection" class="mc_anchor"></a>
<div class="card">
    <h6 class="card-header bg-secondary font-weight-bold">Referral Links</h6>
    <div class="card-body">
		<div class="form-group row">
			<label for="justview1" class="col-sm-3 col-xl-2 col-form-label">Internal Referral URL</label>
			<div class="col-sm-9">
				<div class="input-group input-group-sm">
					<input type="text" name="justview1" id="justview1" value="/#this.link.editClient#&clientReferralID=#local.clientReferralID#" class="form-control form-control-sm" readonly="readonly" onclick="this.select();"/>
					<a target="_blank" href="/#this.link.editClient#&clientReferralID=#local.clientReferralID#" class="ml-1 d-flex align-items-center">Test Link</a>
				</div>
			</div>
		</div>
		<cfif structKeyExists(local, "qryGetClientData") and len(local.qryGetClientData.memberId) GT 0>
			<div class="form-group row">
				<label for="justview2" class="col-sm-3 col-xl-2 col-form-label">External Referral URL</label>
				<div class="col-sm-9">
					<div class="input-group input-group-sm">
						<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?#local.appBaseLink#&ra=#val(local.caseID)?"editCase":"editReferral"#&clientReferralID=#local.clientReferralID#" class="form-control form-control-sm" readonly="readonly" onclick="this.select();"/>
						<a target="_blank" href="/?#local.appBaseLink#&ra=editCase&clientReferralID=#local.clientReferralID#" class="ml-1 d-flex align-items-center">Test Link</a>
					</div>
				</div>
			</div>
		</cfif>
	</div>
</div>
</cfif>

<!-- Modal Content -->
<div class="modal fade" id="emailNotificationModal" tabindex="-1" role="dialog" aria-labelledby="emailNotificationLabel" aria-hidden="true">
	<div class="modal-dialog" role="document">
	<div class="modal-content">
		<div class="modal-header">
		<h5 class="modal-title" id="emailNotificationLabel">Email Notification</h5>
		<button type="button" class="close" data-dismiss="modal" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
		</div>
		<div class="modal-body">
			<p>Would you like to send an email notification to the attorney regarding this status change?</p>
			<p><em>Note: The e-mail will be sent after saving the changes.</em></p>
		</div>
		<div class="modal-footer">
		<button type="button" class="btn btn-secondary" name="DoNotSendEmailBtn" id="DoNotSendEmailBtn">No</button>
		<button type="button" class="btn btn-primary" name="sendEmailBtn" id="sendEmailBtn">Yes</button>
		</div>
	</div>
	</div>
</div>

<div class="modal fade" id="assignToMyselfModal" tabindex="-1" role="dialog" aria-labelledby="assignToMyselfLabel" aria-hidden="true">
	<div class="modal-dialog" role="document">
	<div class="modal-content">
		<div class="modal-header">
		<h5 class="modal-title" id="assignToMyselfLabel">Confirm Assign Yourself as Counselor</h5>
		<button type="button" class="close" data-dismiss="modal" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
		</div>
		<div class="modal-body">
			<p>Are you sure you want to assign the referral to yourself?</p>
		</div>
		<div class="modal-footer">
		<button type="button" class="btn btn-secondary" name="doNotAssignBtn" id="doNotAssignBtn" onclick="$('##assignToMyselfModal').modal('hide');">No</button>
		<button type="button" class="btn btn-primary" name="assignToMeBtn" id="assignToMeBtn">Yes</button>
		</div>
	</div>
	</div>
</div>

<cfloop list="#local.filterFieldNames#" index="local.thisField">
	<cfif isDefined("form.#local.thisField#")>
		<cfif not arguments.event.valueExists('copy')>
			<cfinput type="hidden" name="#LCase(local.thisField)#"  id="#local.thisField#" value="#evaluate("form." & local.thisField)#" />
		<cfelse>
			<cfinput type="hidden" name="#LCase(local.thisField)#"  id="#local.thisField#" value="" />
		</cfif>
	</cfif>
</cfloop>

<cfloop list="#local.lawyerFieldNames#" index="local.thisField">
	<cfif isDefined("form.#local.thisField#")>
		<cfif not arguments.event.valueExists('copy')>
			<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="#evaluate("form." & local.thisField)#" />
		<cfelse>
			<cfinput type="hidden" name="#local.thisField#"  id="#local.thisField#" value="" />
		</cfif>		
	</cfif>
</cfloop>

<script id="mc_counselorsTemplate" type="text/x-handlebars-template">
	{{##if members}}
	<div class="input-group input-group-sm">
		<select name="newCounselorID" id="newCounselorID" class="form-control form-control-sm">
			<option value="">Select a Counselor</option>
			{{##each members}}
				<option value="{{{memberid}}}" data-name="{{{membername}}}" >
					{{{membername}}}
				</option>
			{{/each}}
		</select>
	</div>
	{{/if}}
</script>
</cfoutput>
