<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
						
			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getCampaignList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: '');
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfquery name="local.qryCampaigns" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select cpc.campaignID, cpc.programID, cpc.campaignName, cpc.status, cpc.startDate, cpc.endDate,
				case 
				when exists (select top 1 contributionID from dbo.cp_contributions where campaignID = cpc.campaignID) then 0
				else 1 end as canDelete
			from dbo.cp_campaigns as cpc 
			inner join dbo.cp_programs as cp on cp.programID = cpc.programID
			where cpc.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pID',0)#">
			<cfif arguments.event.getValue('sortByDates',0)>
				order by cpc.endDate desc, cpc.startDate desc, cpc.campaignName asc
			<cfelse>
				order by cpc.campaignName #arguments.event.getValue('orderDir')#
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryCampaigns">
			<cfset local.tmpStr = {
				"campaignID": local.qryCampaigns.campaignID,
				"campaignName": local.qryCampaigns.campaignName & (local.qryCampaigns.status eq 'I' ? " (Inactive)" : ""),
				"campaignDate": len(local.qryCampaigns.startdate) and len(local.qryCampaigns.enddate) ? dateformat(local.qryCampaigns.startDate,"m/d/yyyy") & " - " & dateformat(local.qryCampaigns.endDate,"m/d/yyyy") : "",
				"status": local.qryCampaigns.status,
				"canDelete": local.qryCampaigns.canDelete
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryCampaigns.recordCount),
			"recordsFiltered": val(local.qryCampaigns.recordCount),
			"data": local.data
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCampaignMatrix" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset local = structNew()>
		<cfquery name="local.qryCampaignMatrix" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select mtx.autoID, r.rateID, pf.progFreqID, r.rateName, f.frequency, f.isRecurring, mtx.label, mtx.isRange, 
				mtx.installSetAmt, mtx.installMinAmt, mtx.installMaxAmt, mtx.installDefAmt, mtx.minInstallments, mtx.allowPerpetual, 
				mtx.perpetualAsDefOption, mtx.allowFixedInstallments, mtx.fixedInstallmentsAsDefOption, mtx.adminOnly
			from dbo.cp_campaignMatrix as mtx
			inner join dbo.cp_rates as r on r.rateID = mtx.rateID
			inner join dbo.cp_programFrequencies as pf on pf.progFreqID = mtx.progFreqID
			inner join dbo.cp_frequencies as f on f.frequencyID = pf.frequencyID
			where mtx.campaignID = <cfqueryparam value="#arguments.event.getValue('cid',0)#" cfsqltype="CF_SQL_INTEGER">
			order by r.rateOrder, r.rateID, pf.freqOrder, pf.progFreqID, mtx.label, mtx.autoID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.arrTaxRules = []>
		<cfoutput query="local.qryCampaignMatrix" group="rateID">
			<cfset local.rateRowID = "cm#val(local.qryCampaignMatrix.rateID)#">
			<cfset local.arrTaxRules.append({
				"level": 1,
				"rowType": "rate",
				"rateID": val(local.qryCampaignMatrix.rateID),
				"displayName": local.qryCampaignMatrix.rateName,
				"isRange": local.qryCampaignMatrix.isRange,
				"hasFrequency": val(local.qryCampaignMatrix.autoID) gt 0,
				"DT_RowId": local.rateRowID
			})>
			<cfoutput>
				<cfif val(local.qryCampaignMatrix.autoID) gt 0>
					<cfset local.mtxEncString = ToBase64("#local.qryCampaignMatrix.rateID#|#local.qryCampaignMatrix.progFreqID#|#local.qryCampaignMatrix.isRange#|#local.qryCampaignMatrix.installSetAmt#|#local.qryCampaignMatrix.installMinAmt#|#local.qryCampaignMatrix.installMaxAmt#|#local.qryCampaignMatrix.installDefAmt#|#local.qryCampaignMatrix.minInstallments#|#local.qryCampaignMatrix.label#|#local.qryCampaignMatrix.isRecurring#|#local.qryCampaignMatrix.allowPerpetual#|#local.qryCampaignMatrix.perpetualAsDefOption#|#local.qryCampaignMatrix.allowFixedInstallments#|#local.qryCampaignMatrix.fixedInstallmentsAsDefOption#|#local.qryCampaignMatrix.adminOnly#")>
					<cfset local.arrTaxRules.append({ 
						"level": 2,
						"rowType": "frequency",
						"autoID": val(local.qryCampaignMatrix.autoID),
						"displayName": local.qryCampaignMatrix.frequency & ' (' & local.qryCampaignMatrix.label & ')',
						"installSetAmt": val(local.qryCampaignMatrix.installSetAmt) ? DollarFormat(local.qryCampaignMatrix.installSetAmt) : "",
						"installMinAmt": val(local.qryCampaignMatrix.installMinAmt) ? DollarFormat(local.qryCampaignMatrix.installMinAmt) : "",
						"installMaxAmt": val(local.qryCampaignMatrix.installMaxAmt) ? DollarFormat(local.qryCampaignMatrix.installMaxAmt) : "",
						"installDefAmt": val(local.qryCampaignMatrix.installDefAmt) ? DollarFormat(local.qryCampaignMatrix.installDefAmt) : "",
						"minInstallments": local.qryCampaignMatrix.minInstallments,
						"isRange": local.qryCampaignMatrix.isRange,
						"mtxEncString": local.mtxEncString,
						"DT_RowId": "cm#local.qryCampaignMatrix.rateID#-#local.qryCampaignMatrix.autoID#",
						"DT_RowClass": "child-of-#local.rateRowID#"
					})>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrTaxRules),
			"recordsFiltered": arrayLen(local.arrTaxRules),
			"data": local.arrTaxRules
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getContributionCampaignList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.objContributionAdmin = CreateObject("component","contributionAdmin");
			local.fContributionProgram = arguments.event.getValue('fContributionProgram',0);
			local.campaignName = arguments.event.getValue('fcampaignName','');
			
			local.strCampaignsBaseLinks = {};
			local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');
			local.scheme = arguments.event.getValue('mc_siteInfo.scheme');
		</cfscript>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"cp.programName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"cpc.campaignName #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>
		
		<cfquery name="local.qryContributionCampaigns" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpContributionCampaigns') IS NOT NULL
				DROP TABLE ##tmpContributionCampaigns;
			CREATE TABLE ##tmpContributionCampaigns (programID INT, campaignID INT, applicationInstanceId INT, programName VARCHAR(200), campaignName VARCHAR(200), startDate DATE, endDate DATE, row INT);

			DECLARE @siteID  INT, @totalCount INT, @posStart INT, @posStartAndCount INT;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			INSERT INTO ##tmpContributionCampaigns (programID, campaignID, applicationInstanceId, programName, campaignName, startDate, endDate, row)
			SELECT cp.programID, cpc.campaignID, cp.applicationInstanceID, cp.programName, cpc.campaignName, cpc.startDate, cpc.endDate, ROW_NUMBER() OVER (ORDER BY #local.orderby#)
			FROM dbo.cp_campaigns AS cpc 
			INNER JOIN dbo.cp_programs AS cp on cp.programID = cpc.programID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
			INNER JOIN dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'Contributions'
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = cp.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			WHERE ai.siteID = @siteID
			<cfif val(local.fContributionProgram)>
				AND cp.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.fContributionProgram)#">
			</cfif>
			<cfif Len(local.campaignName)>
				AND cpc.campaignName LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.campaignName#%">
			</cfif>

			SET @totalCount = @@ROWCOUNT;

			SELECT programID, campaignID, applicationInstanceId, programName, campaignName, startDate, endDate, @totalCount AS totalcount
			FROM ##tmpContributionCampaigns
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpContributionCampaigns') IS NOT NULL
				DROP TABLE ##tmpContributionCampaigns;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryContributionCampaigns">
			<cfif NOT StructKeyExists(local.strCampaignsBaseLinks,local.qryContributionCampaigns.applicationInstanceId)>
				<cfset structInsert(local.strCampaignsBaseLinks, local.qryContributionCampaigns.applicationInstanceId, local.objContributionAdmin.getAppBaseLink(applicationInstanceID=local.qryContributionCampaigns.applicationInstanceId))>
			</cfif>
			<cfset local.campaignLink = ToBase64("#local.scheme#://#local.mainhostname#/?#local.strCampaignsBaseLinks[local.qryContributionCampaigns.applicationInstanceId]#&cpAction=showDetail&pid=#local.qryContributionCampaigns.programID#&cid=#local.qryContributionCampaigns.campaignID#")>
				
			<cfset local.tmpStr = {				
				"programID": local.qryContributionCampaigns.programID,
				"campaignID": local.qryContributionCampaigns.campaignID,
				"programName": local.qryContributionCampaigns.programName,
				"campaignName": local.qryContributionCampaigns.campaignName,
				"campaignDate": dateformat(local.qryContributionCampaigns.startDate,"m/d/yyyy") & " " & dateformat(local.qryContributionCampaigns.endDate,"m/d/yyyy"),
				"campaignLink": local.campaignLink
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryContributionCampaigns.totalCount),
			"recordsFiltered": val(local.qryContributionCampaigns.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getContributionList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>
		<cfset local.programIDList = arguments.event.getValue('fProgram','')>
		<cfset local.campaignIDList = arguments.event.getValue('fCampaign','')>
		<cfset local.rateIDList = arguments.event.getValue('fRate','')>
		<cfset local.frequencyIDList = arguments.event.getValue('fFrequency','')>
		<cfset local.statusIDList = arguments.event.getValue('fStatus','')>
		<cfset local.fStartDateFrom = arguments.event.getValue('fStartDateFrom','')>
		<cfset local.fStartDateTo = arguments.event.getValue('fStartDateTo','')>
		<cfset local.fEndDateFrom = arguments.event.getValue('fEndDateFrom','')>
		<cfset local.fEndDateTo = arguments.event.getValue('fEndDateTo','')>
		<cfset local.fHasCardOnFile = arguments.event.getValue('fHasCard','')>
		<cfif len(local.fStartDateFrom)>
			<cfset local.fStartDateFrom = DateFormat(local.fStartDateFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fStartDateTo)>
			<cfset local.fStartDateTo = DateFormat(local.fStartDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fEndDateFrom)>
			<cfset local.fEndDateFrom = DateFormat(local.fEndDateFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fEndDateTo)>
			<cfset local.fEndDateTo = DateFormat(local.fEndDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"membername #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"programName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"startdate #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"startdate #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>
		
		<cfquery name="local.qryMemContributions" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @totalRows int, @contributionIDList varchar(max), @orgID int, @siteID int;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL 
				DROP TABLE ##tmpContributions;
			IF OBJECT_ID('tempdb..##tmpContributions2') IS NOT NULL 
				DROP TABLE ##tmpContributions2;
			IF OBJECT_ID('tempdb..##tmpContributionsPledged') IS NOT NULL 
				DROP TABLE ##tmpContributionsPledged;
			IF OBJECT_ID('tempdb..##tmpContribTransDetails') IS NOT NULL 
				DROP TABLE ##tmpContribTransDetails;
			IF OBJECT_ID('tempdb..##tmpContribInvoices') IS NOT NULL 
				DROP TABLE ##tmpContribInvoices;
			CREATE TABLE ##tmpContributions (programID int, campaignID int, programName varchar(200), campaignName varchar(400), rateName varchar(200), frequency varchar(20), contributionID int PRIMARY KEY, 
				memberID int, isPerpetual bit, startdate date, endDate date, statusName varchar(30), statusCode char(1), membername varchar(210), 
				membercompany varchar(200));
			CREATE TABLE ##tmpContributions2 (programID int, campaignID int, programName varchar(200), campaignName varchar(400), rateName varchar(200), frequency varchar(20), contributionID int PRIMARY KEY, 
				memberID int, membername varchar(210), membercompany varchar(200), isPerpetual bit, startdate date, endDate date, 
				statusName varchar(30), statusCode char(1), totalPledge decimal(18,2), totalPaid decimal(18,2), row int INDEX IX_tmpContributions2_row, 
				payProfileID int, totalRows int, firstPaymentDate date, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2), pledgedValue decimal(18,2));
			CREATE TABLE ##tmpContributionsPledged (contributionID int, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2), pledgedValue decimal(18,2));
			CREATE TABLE ##tmpContribTransDetails (contributionID int, hasInvDues bit, hasInstallmentsToBeConverted bit, hasSchedulesToBeCreated bit);
			CREATE TABLE ##tmpContribInvoices (contributionID int, invoiceID int, amountDue decimal(18,2));

			<cfif arguments.event.getValue('associatedMemberID',0) gt 0>
				DECLARE @memberID int;
				DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY);

				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedMemberID')#">;

				INSERT INTO @tblMembers
				SELECT @memberID as memberID
				FROM dbo.ams_members as m 
				WHERE memberID = @memberID
				
				<cfif arguments.event.getValue('linkedRecords','') is "all">
					UNION
						
					SELECT allchildMember.memberID
					FROM dbo.ams_members as m 
					INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
					INNER JOIN dbo.ams_members as childMember ON childMember.orgID = @orgID and rr.childMemberID = childMember.memberID
					INNER JOIN dbo.ams_members as allchildMember ON allchildMember.orgID = @orgID and allchildMember.activeMemberID = childMember.memberID
					WHERE m.orgID = @orgID
					AND m.memberID = @memberID
					AND childMember.status <> 'D'
				</cfif>
			</cfif>

			INSERT INTO ##tmpContributions (programID, campaignID, programName, campaignName, rateName, frequency, contributionID, memberID, isPerpetual, startdate, endDate, 
				statusName, statusCode, memberName, memberCompany)
			select p.programID, c.campaignID, p.programName, cpc.campaignName, cr.rateName, f.frequency, c.contributionID, mActive.memberID, c.isPerpetual, c.startdate, 
				c.endDate, cps.statusName, cps.statusCode, 
				mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as memberName, mActive.company as memberCompany
			from dbo.sites as s
			inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
			inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
			inner join dbo.cp_contributions as c on c.programID = p.programID
			left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
			inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
			inner join dbo.cp_frequencies as f on f.frequencyID = c.frequencyID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = c.memberID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			left outer join dbo.cp_rates as cr on cr.rateID = c.rateID
			<cfif arguments.event.getValue('associatedMemberID',0) gt 0>
				inner join @tblMembers as m2 on m2.memberID = mActive.memberID
			</cfif>
			<cfif arguments.event.getValue('associatedGroupID',0) gt 0>
				inner join dbo.cache_members_groups mg ON mg.memberID = mActive.memberID 
					and mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedGroupID')#">
			</cfif>
			where s.siteID = @siteID
			<cfif arguments.event.getValue('dtMode') eq "memberContribTab">
				and m.activeMemberID = <cfqueryparam value="#arguments.event.getValue('memberid',0)#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif ListLen(local.programIDList)>
				and p.programID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programIDList#" list="true">)
			</cfif>
			<cfif ListLen(local.campaignIDList)>
				and cpc.campaignID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.campaignIDList#" list="true">)
			</cfif>
			<cfif len(local.fStartDateFrom)>
				and c.startdate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fStartDateFrom#">
			</cfif>
			<cfif len(local.fStartDateTo)>
				and c.startdate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fStartDateTo#">
			</cfif>
			<cfif len(local.fEndDateFrom)>
				and c.enddate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fEndDateFrom#">
			</cfif>
			<cfif len(local.fEndDateTo)>
				and c.enddate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fEndDateTo#">
			</cfif>
			<cfif ListLen(local.frequencyIDList)>
				and c.frequencyID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.frequencyIDList#" list="true">)
			</cfif>
			<cfif ListLen(local.statusIDList)>
				and c.statusID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.statusIDList#" list="true">)
			</cfif>
			<cfif ListLen(local.rateIDList)>
				and c.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateIDList#" list="true">)
			</cfif>
			<cfif len(local.fHasCardOnFile)>
				<cfif local.fHasCardOnFile eq 'Y'>
					and exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
				<cfelseif local.fHasCardOnFile eq 'N'>
					and not exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
				</cfif>
			</cfif>;

			set @totalRows = @@ROWCOUNT;

			INSERT INTO ##tmpContributions2 (programID, campaignID, programName, campaignName, rateName, frequency, contributionID, memberID, memberName, memberCompany, 
				isPerpetual, startdate, endDate, statusName, statusCode, row, totalRows)
			SELECT tmp.programID, tmp.campaignID, tmp.programName, tmp.campaignName, tmp.rateName, tmp.frequency, tmp.contributionID, tmp.memberID, tmp.memberName, tmp.memberCompany, 
				tmp.isPerpetual, tmp.startdate, tmp.endDate, tmp.statusName, tmp.statusCode, tmp.row, @totalRows as totalRows
			FROM (
				SELECT programID, campaignID, programName, campaignName, rateName, frequency, contributionID, memberID, memberName, memberCompany, isPerpetual, startdate, 
					endDate, statusName, statusCode,  
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# , contributionID desc) as row
				FROM ##tmpContributions
			) AS tmp
			WHERE tmp.row > #arguments.event.getValue('posStart')# AND tmp.row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#;
			

			<!--- get pledged amount. 10/2017 - pledged amount should not consider actual transactions/adjustments --->
			select @contributionIDList = COALESCE(@contributionIDList + ',','') + cast(contributionID as varchar(10)) from ##tmpContributions2;
			INSERT INTO ##tmpContributionsPledged (contributionID, totalPledgeFirst, totalPledgeRecurring, pledgedValue)
			EXEC dbo.cp_getContributionAmountSplit @contributionIDList=@contributionIDList;

			UPDATE tmp2
			set tmp2.firstPaymentDate = tmp2FP.firstPaymentDate,
				tmp2.totalPaid = isnull(contribPaid.totalPaid,0),
				tmp2.payProfileID = tmpCC.payProfileID,
				tmp2.totalPledgeFirst = ple.totalPledgeFirst,
				tmp2.totalPledgeRecurring = ple.totalPledgeRecurring,
				tmp2.pledgedValue = ple.pledgedValue
			from ##tmpContributions2 as tmp2
			inner join (
				select cs.contributionID, min(cs.dueDate) as firstPaymentDate
				from dbo.cp_contributionSchedule as cs
				inner join ##tmpContributions2 as tmp on tmp.contributionID = cs.contributionID
				group by cs.contributionID
			) as tmp2FP on tmp2FP.contributionID = tmp2.contributionID
			inner join (
				select tmp.contributionID, max(isnull(cpp.payProfileID,0)) as payProfileID
				from ##tmpContributions2 as tmp
				left outer join dbo.cp_contributionPayProfiles as cpp on cpp.contributionID = tmp.contributionID
				group by tmp.contributionID
			) as tmpCC on tmpCC.contributionID = tmp2.contributionID
			cross apply dbo.fn_cp_totalFeeAndPaid(@orgID,tmp2.contributionID) as contribPaid
			inner join ##tmpContributionsPledged as ple on ple.contributionID = tmp2.contributionID;

			INSERT INTO ##tmpContribInvoices (contributionID, invoiceID, amountDue)
			select tmp.contributionID, it.invoiceID, sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) as amountDue
			from ##tmpContributions2 as tmp
			cross apply dbo.fn_cp_contributionTransactions(tmp.contributionID) as ct
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ct.transactionID
			inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
			where tmp.isPerpetual = 0
			and tmp.statusCode <> 'D'
			and invs.status in ('Open','Closed','Delinquent')
			group by tmp.contributionID, it.invoiceID
			having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;

			INSERT INTO ##tmpContribTransDetails (contributionID, hasInvDues, hasInstallmentsToBeConverted, hasSchedulesToBeCreated)
			select contributionID, 
				case when exists (select 1 from ##tmpContribInvoices where contributionID = tmp.contributionID) then 1 else 0 end as hasInvDues,
				case 
				when exists (
					select 1
					from dbo.tr_transactionInstallments as ti
					inner join dbo.cp_contributionSchedule as cs on cs.scheduleID = ti.scheduleID
					where ti.orgID = @orgID 
					and cs.contributionID = tmp.contributionID
					and ti.isActive = 1
					and ti.isConverted = 0
					and ti.isPaidOnCreate = 0
					) then 1 
				else 0 
				end as hasInstallmentsToBeConverted,
				case 
				when exists (
					select 1
					from dbo.cp_contributionSchedule as cs
					left outer join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.scheduleID = cs.scheduleID
					where cs.contributionID = tmp.contributionID
					and ti.installmentID is null
					) then 1 
				else 0 
				end as hasSchedulesToBeCreated
			from ##tmpContributions2 as tmp
			where tmp.isPerpetual = 0
			and tmp.statusCode <> 'D';

			select c.programID, c.campaignID, c.programName, c.campaignName, c.rateName, c.frequency, c.contributionID, c.memberID, c.memberName, c.memberCompany, 
				c.isPerpetual, c.startdate, c.endDate, c.statusName, c.statusCode, c.totalPledgeFirst, c.totalPledgeRecurring, 
				c.pledgedValue, c.firstPaymentDate, c.totalPaid, c.row, cts.hasInvDues, cts.hasInstallmentsToBeConverted, 
				cts.hasSchedulesToBeCreated, c.totalRows, c.payProfileID, 
				showPayIcon = case 
					when c.isPerpetual = 1 then 1
					when cts.hasInvDues = 1 or cts.hasSchedulesToBeCreated = 1 or cts.hasInstallmentsToBeConverted = 1 then 1
					else 0 end,
				totalInvDue = case 
					when cts.hasInvDues = 1 and cts.hasSchedulesToBeCreated = 0 and cts.hasInstallmentsToBeConverted = 0 
						then (select sum(amountDue) from ##tmpContribInvoices where contributionID = c.contributionID) 
					else 0 end,
				invoicesDue = case 
					when cts.hasInvDues = 1 and cts.hasSchedulesToBeCreated = 0 and cts.hasInstallmentsToBeConverted = 0 
						then (select substring((
							select ','+ cast(invoiceID as varchar(10)) AS [text()]
							from ##tmpContribInvoices
							where contributionID = c.contributionID
							For XML PATH ('')
							), 2, 2000)) 
					else '' end
			from ##tmpContributions2 as c
			left outer join ##tmpContribTransDetails as cts on c.contributionID = cts.contributionID
			order by c.row;

			IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL 
				DROP TABLE ##tmpContributions;
			IF OBJECT_ID('tempdb..##tmpContributions2') IS NOT NULL 
				DROP TABLE ##tmpContributions2;
			IF OBJECT_ID('tempdb..##tmpContributionsPledged') IS NOT NULL 
				DROP TABLE ##tmpContributionsPledged;
			IF OBJECT_ID('tempdb..##tmpContribTransDetails') IS NOT NULL 
				DROP TABLE ##tmpContribTransDetails;
			IF OBJECT_ID('tempdb..##tmpContribInvoices') IS NOT NULL 
				DROP TABLE ##tmpContribInvoices;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfif local.qryMemContributions.recordCount>
			<cfloop query="local.qryMemContributions">
				<cfsavecontent  variable="local.contributionDetail">
				<cfoutput>
					<cfif arguments.event.getValue('dtMode') neq "programContribTab">
						#local.qryMemContributions.programName# (#local.qryMemContributions.frequency#)<br/>
						<cfif len(local.qryMemContributions.campaignName)>#local.qryMemContributions.campaignName#<br/></cfif>
						<cfif len(local.qryMemContributions.rateName)>#local.qryMemContributions.rateName#<br/></cfif>
					<cfelse>
						<cfif len(local.qryMemContributions.campaignName)>#local.qryMemContributions.campaignName#<br/></cfif>
						<cfif len(local.qryMemContributions.rateName)>#local.qryMemContributions.rateName#<br/></cfif>
						#local.qryMemContributions.frequency#<br/>
					</cfif>
					<cfif local.qryMemContributions.totalPledgeRecurring gt 0>
						First: #replace(DollarFormat(local.qryMemContributions.totalPledgeFirst),".00","")# on #DateFormat(local.qryMemContributions.firstPaymentDate, "m/d/yyyy")#<br/>
						Recurring: #replace(DollarFormat(local.qryMemContributions.totalPledgeRecurring),".00","")# 
							<cfif local.qryMemContributions.isPerpetual>
								perpetually
							<cfelse>
								until #DateFormat(local.qryMemContributions.endDate, "m/d/yyyy")#
							</cfif><br/>
					<cfelse>
						#replace(DollarFormat(local.qryMemContributions.totalPledgeFirst),".00","")# on #DateFormat(local.qryMemContributions.firstPaymentDate, "m/d/yyyy")#<br/>
					</cfif>

					<cfswitch expression="#local.qryMemContributions.statusName#">
						<cfcase value="Fulfilled">
							<cfset local.className = "badge-success">
						</cfcase>
						<cfcase value="Cancelled">
							<cfset local.className = "badge-dark">
						</cfcase>
						<cfcase value="Delinquent">
							<cfset local.className = "badge-danger">
						</cfcase>
						<cfdefaultcase>
							<cfset local.className = "badge-info">
						</cfdefaultcase>
					</cfswitch>
					<span class="badge #local.className#">#local.qryMemContributions.statusName#</span>
				</cfoutput>	
				</cfsavecontent>
				<cfif local.qryMemContributions.totalInvDue gt 0>
					<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryMemContributions.memberID, t="#local.qryMemContributions.programName# - #local.qryMemContributions.frequency#", ta=local.qryMemContributions.totalInvDue, tmid=local.qryMemContributions.memberID, ad="v|#local.qryMemContributions.invoicesDue#")>
				<cfelse>
					<cfset local.addPaymentEncString = "">
				</cfif>
				<cfset local.tmpStr = {
					"contributionID": local.qryMemContributions.contributionID,
					"contributionDetail": local.contributionDetail,
					"memberID": local.qryMemContributions.memberID,
					"memberName": local.qryMemContributions.memberName,
					"statusCode": local.qryMemContributions.statusCode,
					"memberCompany": local.qryMemContributions.memberCompany,
					"startDate": DateFormat(local.qryMemContributions.startDate, "m/d/yyyy"),
					"isPerpetual": local.qryMemContributions.isPerpetual,
					"pledgedValue": DollarFormat(local.qryMemContributions.pledgedValue),
					"totalPaid": DollarFormat(local.qryMemContributions.totalPaid),
					"showPayProfile": listFindNoCase("P,A,Q",local.qryMemContributions.statusCode),
					"payProfileID": val(local.qryMemContributions.payProfileID),
					"statusCode": local.qryMemContributions.statusCode,
					"showPayIcon": local.qryMemContributions.showPayIcon,
					"totalInvDue": local.qryMemContributions.totalInvDue,
					"addPaymentEncString": local.addPaymentEncString,
					"programID": local.qryMemContributions.programID,
					"campaignID": val(local.qryMemContributions.campaignID),
					"DT_RowId": "contribution_#local.qryMemContributions.contributionID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": val(local.qryMemContributions.totalRows),
			"recordsFiltered": val(local.qryMemContributions.totalRows),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProgramList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.objContributionAdmin = CreateObject("component","contributionAdmin");
			local.fContributionProgram = arguments.event.getValue('fContributionProgram',0);
			local.campaignName = arguments.event.getValue('fcampaignName','');

			local.strCampaignsBaseLinks = {};
			local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');
			local.scheme = arguments.event.getValue('mc_siteInfo.scheme');
		</cfscript>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"programName #arguments.event.getValue('orderDir')#")>

		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryPrograms" datasource="#application.dsn.memberCentral.dsn#" result="local.qryProgramsResult">
			SET NOCOUNT ON;
			DECLARE @totalPrograms int;

			IF OBJECT_ID('tempdb..##tmpPrograms') IS NOT NULL 
				DROP TABLE ##tmpPrograms;
			CREATE TABLE ##tmpPrograms (programID int PRIMARY KEY, programName varchar(400), applicationInstanceName varchar(400), 
				status varchar(8), isProgramEnabled bit);

			insert into ##tmpPrograms (programID, programName, applicationInstanceName, status, isProgramEnabled)
			select cp.programID, cp.programName, ai.applicationInstanceName, srs.siteResourceStatusDesc, cp.isProgramEnabled
			from dbo.cp_programs as cp
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'Contributions'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = cp.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfif val(arguments.event.getValue('fAppInstanceID',0)) gt 0>
				and ai.applicationInstanceID = <cfqueryparam value="#val(arguments.event.getValue('fAppInstanceID'))#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif arguments.event.getValue('fHideDeleted',0) eq 1>
				and srs.siteResourceStatusDesc <> 'Deleted'		
			</cfif>
			<cfif len(arguments.event.getTrimValue('fProgramName','')) gt 0>
				and cp.programName like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('fProgramName')#%">
			</cfif>;

			SELECT @totalPrograms = @@ROWCOUNT;

			SELECT tmp.programID, tmp.programName, tmp.applicationInstanceName, tmp.status, tmp.isProgramEnabled, 
				count(c.contributionID) as contributionCount, @totalPrograms as totalPrograms
			FROM (
				SELECT programID, programName, applicationInstanceName, status, isProgramEnabled, 
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# ) as row  
				FROM ##tmpPrograms
			) AS tmp 
			LEFT OUTER JOIN dbo.cp_contributions as c on c.programID = tmp.programID
			WHERE tmp.row > #arguments.event.getValue('posStart')# AND tmp.row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
			GROUP BY tmp.programID, tmp.programName, tmp.applicationInstanceName, tmp.status, tmp.isProgramEnabled, tmp.row
			ORDER BY tmp.row;

			IF OBJECT_ID('tempdb..##tmpPrograms') IS NOT NULL 
				DROP TABLE ##tmpPrograms;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryPrograms">
			<cfset local.cpTitle = htmlEditFormat(local.qryPrograms.programName)>
			<cfset local.tmpStr = {
				"programID": local.qryPrograms.programID,
				"programName": local.cpTitle,
				"applicationInstanceName": local.qryPrograms.applicationInstanceName,
				"contributionCount": local.qryPrograms.contributionCount,
				"isProgramEnabled": local.qryPrograms.isProgramEnabled,
				"status": local.qryPrograms.status,
				"canEdit": local.qryPrograms.status neq "Deleted",
				"canDelete": local.qryPrograms.status neq "Deleted" and (local.qryPrograms.status neq "Inactive" or local.qryPrograms.contributionCount eq 0),
				"DT_RowId": "cp_program_#local.qryPrograms.programID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryPrograms.totalPrograms),
			"recordsFiltered": val(local.qryPrograms.totalPrograms),
			"data": local.data
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProgramFrequencyList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"frequency #arguments.event.getValue('orderDir')#")>

		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryProgramFrequency" datasource="#application.dsn.memberCentral.dsn#">
			SELECT cf.frequencyID, cf.frequency,
				CASE 
				WHEN EXISTS(select 1 from dbo.cp_programFrequencies where frequencyID = cf.frequencyID) THEN 1
				ELSE 0 end as isTiedToProgram
			FROM dbo.cp_frequencies as cf
			WHERE cf.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY #preserveSingleQuotes(local.orderby)#
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryProgramFrequency">
			<cfset local.tmpStr = {
				"frequencyID": local.qryProgramFrequency.frequencyID,		
				"frequency": local.qryProgramFrequency.frequency,
				"isTiedToProgram": local.qryProgramFrequency.isTiedToProgram,
				"DT_RowId": "row_freq_#local.qryProgramFrequency.frequencyID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"data": local.data
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProgramRates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateData">
					UPDATE cp_rates
					SET rateOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					WHERE programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
					AND rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>
		<cfquery name="local.qryProgramRates" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select cpr.rateID, cpr.rateName,
				case when exists (select top 1 contributionID from dbo.cp_contributions where programID = cpr.programID and rateID = cpr.rateID) then 1
					else 0 end as isTiedToContribution,
				case when exists (select top 1 autoID from dbo.cp_campaignMatrix where rateID = cpr.rateID) then 1
					else 0 end as isTiedToCampaign
			from dbo.cp_rates as cpr
			where cpr.programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
			order by cpr.rateOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryProgramRates">
			<cfset local.tmpStr = {
				"rateID": local.qryProgramRates.rateID,
				"rateName": local.qryProgramRates.rateName,
				"canDelete": local.qryProgramRates.isTiedToContribution is 0 and local.qryProgramRates.isTiedToCampaign is 0,
				"DT_RowId": "row_cpr#local.qryProgramRates.rateID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryProgramRates.recordcount),
			"recordsFiltered": val(local.qryProgramRates.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProgramDistributions" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc')>
		
		<cfquery name="local.qryProgramDistribution" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select cd.distribID, cd.programID, cd.distDesc, tg.AccountCode, cd.distPct, cd.distCode,
				case when exists (select top 1 campaignID from dbo.cp_campaignDistributions where distribid = cd.distribID) then 1
					else 0 end as isTiedToCampaign,
				case when exists (select top 1 contribDistributionID from dbo.cp_contributionDistributions where distribid = cd.distribID) then 1
					else 0 end as isTiedToContribution
			from dbo.cp_distributions as cd
			inner join dbo.tr_glaccounts as tg on tg.GLAccountID = cd.GLAccountID
			where cd.programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
			order by cd.distDesc #arguments.event.getValue('orderDir')#;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryProgramDistribution">
			<cfset local.tmpStr = {
				"distribID": local.qryProgramDistribution.distribID,
				"distDesc": local.qryProgramDistribution.distDesc,
				"distCode": local.qryProgramDistribution.distCode,
				"AccountCode": local.qryProgramDistribution.AccountCode,
				"distPct": len(local.qryProgramDistribution.distPct) ? '#local.qryProgramDistribution.distPct# %' : '',
				"canDelete": local.qryProgramDistribution.isTiedToCampaign is 0 and local.qryProgramDistribution.isTiedToContribution is 0
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryProgramDistribution.recordcount),
			"recordsFiltered": val(local.qryProgramDistribution.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProgramFrequencies" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
			
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateData">
					UPDATE cpf
					SET cpf.freqOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					from dbo.cp_programFrequencies as cpf 
					inner join dbo.cp_frequencies as cf on cf.frequencyID = cpf.frequencyID
					where cpf.programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
					and cf.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					AND cpf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfquery name="local.qryProgramFrequency" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select cpf.progFreqID, cpf.programID, cpf.frequencyID, cf.frequency, cf.isRecurring, cf.numMonths,
				case when exists (select top 1 autoID from dbo.cp_campaignMatrix where progFreqID = cpf.progFreqID) then 1
					else 0 end as isTiedToCampaign
			from dbo.cp_programFrequencies as cpf 
			inner join dbo.cp_frequencies as cf on cf.frequencyID = cpf.frequencyID
			where cpf.programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
			and cf.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			order by cpf.freqOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryProgramFrequency">
			<cfset local.tmpStr = {
				"progFreqID": local.qryProgramFrequency.progFreqID,
				"frequency": local.qryProgramFrequency.frequency,
				"recurring": local.qryProgramFrequency.isRecurring ? 'Recurring every #local.qryProgramFrequency.numMonths# #(local.qryProgramFrequency.numMonths is not 1 ? "months" : "month")#' : 'One-Time',
				"canMoveUp": local.qryProgramFrequency.currentRow neq 1,
				"canMoveDown": local.qryProgramFrequency.currentRow neq local.qryProgramFrequency.recordcount,
				"canDelete": local.qryProgramFrequency.isTiedToCampaign is 0,
				"DT_RowId": "row_cpf#local.qryProgramFrequency.progFreqID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryProgramFrequency.recordcount),
			"recordsFiltered": val(local.qryProgramFrequency.recordcount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getContributionInstallmentDetails" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"tmp.dueDate #arguments.event.getValue('orderDir')#, tmp.scheduleID #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryContributionInstallments" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpContribInstallDetails') IS NOT NULL 
				DROP TABLE ##tmpContribInstallDetails;
			CREATE TABLE ##tmpContribInstallDetails (scheduleID int, dueDate date, installmentAmount decimal(18,2), isConvertedToSale bit, row int);

			DECLARE @contributionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cid',0)#">,
				@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
				@totalCount int;

			WITH allSched AS (
				select cs.scheduleID, cs.DueDate, t.amount, ti.isConverted, ti.isPaidOnCreate, ti.transactionID
				from dbo.cp_contributionSchedule as cs 
				inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.scheduleID = cs.scheduleID 
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = ti.transactionID and t.statusID = 1
				where cs.contributionID = @contributionID
				and ti.isActive = 1
			), contribTrans AS (
				select transactionID, scheduleID
				from dbo.fn_cp_contributionTransactions(@contributionID)
				where typeID in (1,3,7,10)
			)
			INSERT INTO ##tmpContribInstallDetails (scheduleID, dueDate, installmentAmount, isConvertedToSale, row)
			select tmp.scheduleID, tmp.dueDate, sum(tmp.installmentAmount) as installmentAmount, tmp.isConverted,
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#)
			from (
				select scheduleID, sum(amount) as installmentAmount, dueDate, isConverted
				from allSched
				where isConverted = 0 
				and isPaidOnCreate = 0
				group by scheduleID, dueDate, isConverted
					union all
				select scheduleID, sum(amount) as installmentAmount, dueDate, isConverted
				from allSched
				where isConverted = 1 
				and isPaidOnCreate = 1
				group by scheduleID, dueDate, isConverted
					union all
				select allSched.scheduleID, sum(tFull.cache_amountAfterAdjustment) as installmentAmount, allSched.dueDate, allSched.isConverted
				from allSched
				inner join contribTrans on contribTrans.scheduleID = allSched.scheduleID
				cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,contribTrans.transactionID) as tFull
				where allSched.isConverted = 1 
				and allSched.isPaidOnCreate = 0
				group by allSched.scheduleID, allSched.dueDate, allSched.isConverted
			) tmp
			group by tmp.scheduleID, tmp.dueDate, tmp.isConverted;

			select @totalCount = @@ROWCOUNT;

			select scheduleID, dueDate, installmentAmount, isConvertedToSale, row, @totalCount as totalCount
			from ##tmpContribInstallDetails
			where row > #arguments.event.getValue('posStart')# AND row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
			order by row;

			IF OBJECT_ID('tempdb..##tmpContribInstallDetails') IS NOT NULL 
				DROP TABLE ##tmpContribInstallDetails;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryContributionInstallments">
			<cfset local.tmpStr = {
				"scheduleID": local.qryContributionInstallments.scheduleID,
				"dueDate": dateFormat(local.qryContributionInstallments.dueDate,'m/d/yyyy'),
				"installmentAmount": dollarFormat(local.qryContributionInstallments.installmentAmount),
				"isFirstRow": val(local.qryContributionInstallments.row) eq 1 ? 1 : 0,
				"isConvertedToSale": local.qryContributionInstallments.isConvertedToSale,
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryContributionInstallments.totalCount),
			"recordsFiltered": val(local.qryContributionInstallments.totalCount),
			"data": local.data
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getContributionChangeLogList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset local.qryChangeLogs = CreateObject("component","contributions").getContributionsChangeLogFromFilters(event=arguments.event, mode='grid')>

		<cfset local.data = []>
		<cfif local.qryChangeLogs.recordCount>
			<cfloop query="local.qryChangeLogs">
				<cfsavecontent variable="local.contributionDetail">
				<cfoutput>
					<cfif arguments.event.getValue('dtMode') neq "programContribTab">
						#local.qryChangeLogs.programName# (#local.qryChangeLogs.frequency#)<br/>
						<cfif len(local.qryChangeLogs.campaignName)>#local.qryChangeLogs.campaignName#<br/></cfif>
						<cfif len(local.qryChangeLogs.rateName)>#local.qryChangeLogs.rateName#<br/></cfif>
					<cfelse>
						<cfif len(local.qryChangeLogs.campaignName)>#local.qryChangeLogs.campaignName#<br/></cfif>
						<cfif len(local.qryChangeLogs.rateName)>#local.qryChangeLogs.rateName#<br/></cfif>
						#local.qryChangeLogs.frequency#<br/>
					</cfif>
					
					<cfif local.qryChangeLogs.totalPledgeRecurring gt 0>
						First: #replace(DollarFormat(local.qryChangeLogs.totalPledgeFirst),".00","")# on #DateFormat(local.qryChangeLogs.firstPaymentDate, "m/d/yyyy")#<br/>
						Recurring: #replace(DollarFormat(local.qryChangeLogs.totalPledgeRecurring),".00","")# 
							<cfif local.qryChangeLogs.isPerpetual>
								perpetually
							<cfelse>
								until #DateFormat(local.qryChangeLogs.endDate, "m/d/yyyy")#
							</cfif><br/>
					<cfelse>
						#replace(DollarFormat(local.qryChangeLogs.totalPledgeFirst),".00","")# on #DateFormat(local.qryChangeLogs.firstPaymentDate, "m/d/yyyy")#<br/>
					</cfif>

					<cfswitch expression="#local.qryChangeLogs.statusName#">
						<cfcase value="Fulfilled">
							<cfset local.className = "badge-success">
						</cfcase>
						<cfcase value="Cancelled">
							<cfset local.className = "badge-dark">
						</cfcase>
						<cfcase value="Delinquent">
							<cfset local.className = "badge-danger">
						</cfcase>
						<cfdefaultcase>
							<cfset local.className = "badge-info">
						</cfdefaultcase>
					</cfswitch>
					<span class="badge #local.className#">#local.qryChangeLogs.statusName#</span>
				</cfoutput>	
				</cfsavecontent>
				
				<cfset local.tmpStr = {
					"contributionID": local.qryChangeLogs.contributionID,
					"contributionDetail": local.contributionDetail,
					"memberID": local.qryChangeLogs.memberID,
					"memberName": local.qryChangeLogs.memberName,
					"memberCompany": local.qryChangeLogs.memberCompany,
					"currentStatus": local.qryChangeLogs.currentStatus,
					"previousStatus": local.qryChangeLogs.previousStatus,
					"updateDate": DateTimeFormat(local.qryChangeLogs.updateDate, "m/d/yy h:nn tt"),
					"actorMemberName": local.qryChangeLogs.actorMemberName,
					"DT_RowId": "changeLogRow_#local.qryChangeLogs.row#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": val(local.qryChangeLogs.totalRows),
			"recordsFiltered": val(local.qryChangeLogs.totalRows),
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getContributionAppInstanceList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir', form['order[0][dir]'] ?: 'asc');
		</cfscript>

		 <cfquery name="local.qryContributionAppInstances" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			SELECT ci.applicationInstanceID, ci.applicationInstanceName, sr.siteResourceID, page.pageName, page.siteResourceID as pageResourceID,
				CASE WHEN EXISTS (SELECT 1 FROM dbo.cp_programs cp WHERE cp.applicationInstanceID = ci.applicationInstanceID) THEN 0 ELSE 1 END AS isDeletable
			FROM dbo.cms_applicationInstances AS ci
			INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ci.applicationTypeID 
				AND at.applicationTypeName = 'Contributions'
			INNER JOIN dbo.cms_siteResources as sr on sr.siteID = @siteID 
				and sr.siteResourceID = ci.siteResourceID
				and sr.siteResourceStatusID = 1
			INNER JOIN dbo.cms_siteResources as pageResource on pageResource.siteID = @siteID
				and sr.parentSiteResourceID = pageResource.siteResourceID
				and pageResource.siteResourceStatusID = 1
			INNER JOIN dbo.cms_pages as page ON page.siteID = @siteID
				and page.siteResourceID = pageResource.siteResourceID
			WHERE ci.siteID = @siteID
			ORDER BY ci.applicationInstanceName #arguments.event.getValue('orderDir')#;
		</cfquery>

		<cfset local.data = []>
		<cfif local.qryContributionAppInstances.recordCount>
			<cfloop query="local.qryContributionAppInstances">                
				<cfset local.tmpStr = {
					"applicationInstanceID": local.qryContributionAppInstances.applicationInstanceID,
					"applicationInstanceName": local.qryContributionAppInstances.applicationInstanceName,
					"siteResourceID": local.qryContributionAppInstances.siteResourceID,
					"pageName": local.qryContributionAppInstances.pageName,
					"pageResourceID": local.qryContributionAppInstances.pageResourceID,
					"isDeletable": local.qryContributionAppInstances.isDeletable,
					"DT_RowId": "row_con_#local.qryContributionAppInstances.applicationInstanceID#"
				}>
				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw', 1))),
			"recordsTotal": val(local.qryContributionAppInstances.recordCount),
			"recordsFiltered": val(local.qryContributionAppInstances.recordCount),
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

</cfcomponent>