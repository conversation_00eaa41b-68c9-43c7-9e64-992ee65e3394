<cfsavecontent variable="local.responseJS">
	<cfoutput>
	<script type="text/javascript">
		let responsesListTable;
		let sw_sitecode = '#local.siteCode#';
		
		function initResponsesListTable() {
			let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			
			responsesListTable = $('##responsesListTable').DataTable({
				"processing": true,
				"serverSide": true,
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.listResponsesLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								if(data.memberid)
									renderData += '<a href="#local.editmemberlink#&memberID='+data.memberid+'">'+data.lastName+', '+data.firstName+' ('+data.memberNumber+')</a>';
								else 
									renderData += data.lastName+', '+data.firstName;
								if (data.company.length) 
									renderData += '<div class="text-dim small">'+data.company+'</div>';
								if (data.signuporgcode.toUpperCase() != sw_sitecode) 
									renderData += '<div class="text-dim small">'+data.signuporgcode+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "35%",
						"className": "align-top"
					},
					{ 
						"data": "dateCompleted", 
						"width": "12%",
						"className": "align-top"
					},
					{ 
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								<cfif local.qryGetForm.formTypeAbbr EQ "V">
									renderData += data.eventName;
								<cfelse>
									renderData += data.seminarName;
								</cfif>
								<cfif local.qryGetForm.formTypeAbbr EQ "E">
									renderData += '<div class="small text-dim">'+data.loadPoint+'</div>';
								</cfif>
							}
							return type === 'display' ? renderData : data;
						}, 
						"className": "align-top"
					},
					<cfif local.qryGetForm.formTypeAbbr EQ "E">
						{ "data": "passingPct", 'className': 'text-center',"width": "8%", "className": "align-top" },
					</cfif>
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="" class="btn btn-xs btn-outline-primary p-1 m-1"  onclick="viewResponse('+data.responseID+');return false;" title="View Response"><i class="fa-solid fa-eye"></i></a> ';
								if(data.candelete) 
									renderData += '<a href="##" id="btnDelRs'+data.responseID+'" class="btn btn-xs btn-outline-danger p-1 m-1"  onclick="removeResponse('+data.responseID+');return false;" title="Delete Response" ><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "16%",
						"className": "align-top",
						"orderable": false 
					}
				],
				"order": [[1, 'DESC']]
			});
		}
		function viewResponse(rID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'View Response',
				iframe: true,
				contenturl: '#local.viewResponseLink#&responseID='+ rID,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: false,
					buttons: [
						{
							class: 'btn-sm btn-secondary ml-auto',
							name: 'btnPrintResponse',
							id: 'btnPrintResponse',
							label: 'Print',
							iconclass: 'fa-solid fa-print',
							clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.printFormResponse'
						}
					]
				}
			});	
		}	
        function removeResponse(rID) { 
			var removeResponseResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					responsesListTable.draw();
				} else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('We were unable to delete this response. Try again.');
				}
			};

			let delBtn = $('##btnDelRs'+rID);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { formID:#local.formID#, rid:rID };
				TS_AJX('ADMEVALUATION','removeResponse',objParams,removeResponseResult,removeResponseResult,10000,removeResponseResult);
			});
		}
		function filterResponses() {
			if (!$('##divFilterForm').is(':visible')) {
				$('##divFilterForm').show();
			}
		}
		function dofilterResponses() {
			responsesListTable.draw();
			
		}
		function clearOrderSearch() {
			$('##frmFilter')[0].reset();
			responsesListTable.draw();
		}
		$(function() {
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupCalendarIcons('frmFilter');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.responseJS)#">

<cfoutput>
<!--- button bar --->
<div class="toolButtonBar">
	<div><a href="javascript:filterResponses();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter responses."><i class="fa-regular fa-filter"></i> Filter Responses</a></div>
	
</div>
<div id="divFilterForm" style="display:none;">
	<form name="frmFilter" id="frmFilter" onsubmit="dofilterResponses();return false;">
		<input type="hidden" name="isSearch" id="isSearch" value="0">
		
		<div class="row mb-3">
			<div class="col-md-12 pr-md-1">
				<div class="card card-box h-100">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-md">
							Filter Responses
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-row">
							<div class="col-xl-6 col-md-12 px-md-1">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<cfif local.qryGetForm.formTypeAbbr EQ "V">
											<input type="text" name="eventName" id="eventName" value="" class="form-control">
											<label for="eventName">Event Name Contains...</label>
										<cfelse>
											<input type="text" name="seminarName" id="seminarName" value="" class="form-control">
											<label for="seminarName">Seminar Name Contains...</label>
										</cfif>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row mt-3">
							<div class="col-xl-3 col-md-12">
								<div class="form-group">
									<div class="form-label-group">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fDateFrom" id="fDateFrom" value="" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fDateFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fDateFrom">Response Date From</label>
										</div>
									</div>
								</div>
							</div>

							<div class="col-xl-3 col-md-12">
								<div class="form-group">
									<div class="form-label-group">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fDateTo" id="fDateTo" value="" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fDateTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fDateTo">Response Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="card-footer text-right p-2">
						<button type="button" name="btnClear" id="btnClear" class="btn btn-sm btn-secondary" onclick="clearOrderSearch();">Clear Filters</button>
						
						<button type="submit" class="btn btn-sm btn-primary">Show Responses</button>
					</div>
				</div>
			</div>
			
		</div>
	</form>
</div>
<div id="divResponseGridContainer">
	<table id="responsesListTable" class="table table-sm table-striped table-bordered mt-4" style="width:100%">
		<thead>
			<tr>
				<th>Respondent </th>
				<th>Response Date</th>
				<cfif local.formTypeAbbr EQ 'V'>
					<th>Event</th>
				<cfelse>
					<th>Seminar</th>
				</cfif>
				<cfif local.qryGetForm.formTypeAbbr EQ "E">
					<th>Score</th>
				</cfif>    
				<th>Actions</th>
			</tr>
		</thead>
	</table>
</div>
</cfoutput>