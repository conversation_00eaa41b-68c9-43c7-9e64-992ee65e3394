@charset "utf-8";
 *,
input[type="search"] {
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-o-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
body {
	margin: 0;
	padding: 0;
	
	font-style: normal;
	font-weight: 400;
	font-size: 24px;
	line-height: 30px;
	color: #1b2b54;
}
img {
	max-width: 100%;
}
select, 
textarea, 
input[type="text"], 
input[type="password"], 
input[type="datetime"], 
input[type="datetime-local"], 
input[type="date"], 
input[type="month"], 
input[type="time"], 
input[type="week"], 
input[type="number"], 
input[type="email"], 
input[type="url"], 
input[type="search"], 
input[type="tel"], 
input[type="color"], 
.uneditable-input {
	min-height: 30px;
}
.wrapper a:focus {
	text-decoration: none;
	outline: none;
}
.container {width: 1140px;}
.container-fluid {
	width: 100%;
	max-width: 1480px;
	margin: 0 auto;
}
.TitleText {
	font-family: "Merriweather", serif;
	font-size: 40px;
	font-style:normal;
	font-weight: 400;
	line-height: 1.4;
	margin: 0;
	text-decoration: none;
	color: #000000;
}
.ListHeader {
	font-family: "Merriweather", serif;
	font-size: 23px;
	font-style:normal;
	font-weight: 400;
	line-height: 1.2;
	margin: 0 0 15px;
	text-decoration: none;
	color: #000000;
}
.ListHeader:after {
    display: block;
    width: 200px;
    height: 3px;
    background: #A08B60;
    content: "";
    margin: 15px 0 0;
}

.SubHeading,
.SubHeadline {
	font-family: "Montserrat", sans-serif;
	font-size: 16px;
	line-height: 1.2;
	color: #263A9A;
	margin: 0 0 10px;
	text-decoration: none;
	font-weight: 700;
	gap: 15px;
	text-transform: uppercase;
	letter-spacing: 0.03em;
	display: inline-block;
}

.HeaderText {
	font-family: "Merriweather", serif;
	font-size: 32px;
	font-style: normal;
	font-weight: 400;
	line-height: 1.2;
	text-decoration: none;
	color: #000000;
	margin: 0 0 20px;
}
.HeaderText:after {
    display: block;
    width: 200px;
    height: 3px;
    background: #A08B60;
    content: "";
    margin: 20px 0 0;
}
.HeaderTextSmall {
	font-family: "Merriweather", serif;
	font-style: normal;
	font-weight: 400;
	font-size: 28px;
	line-height: 1.4;
	color: #000;
	letter-spacing: 0;
	text-decoration: none;
	margin: 0 0 30px;
}
.HeaderTextSmall:after {
    display: block;
    width: 200px;
    height: 3px;
    background: #A08B60;
    content: "";
    margin: 15px 0 0;
}
.BulletHeader {
	font-family: "Merriweather", serif;
	font-style: normal;
	font-weight: bold;
	font-size: 28px;
	line-height: 50px;
	color: #A08B60;
	margin: 0 0 15px;
	text-decoration: none;
	text-transform: uppercase;
}

.SectionHeader {
	position: relative;
	margin-bottom: 50px;
	font-size: 30px;
	color: #005187;
	background: #ddeffd;
	text-transform: uppercase;
	/* text-align: center; */
	/* font-weight: normal; */
	/* display: flex; */
	/* align-items: center; */
	line-height: 1.2;
	padding: 5px;
	font-weight: 700;
	text-align: center;
	font-family: "Merriweather", serif;
}

.ColumnHeader {
    color: #000000;
    font-size: 18px;
    line-height: 1.2;
    font-weight: 400;
    position: relative;
    width: auto;
    font-family: "Merriweather", serif;
    display: inline-block;
    letter-spacing: 0.05em;
}
h1 a,h2 a,h3 a,h4 a,h5 a,h6 a {
	color: #1b2b54;
	text-decoration: underline;
}
h1,h2,h3,h4,h5,h6 {
	color: #000000;
	font-family: "Merriweather", serif;
	line-height: 1.2;
}

.Merriweather{font-family: "Merriweather", serif !important;}


body, p, .BodyText {
	font-family: "Montserrat", sans-serif;
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 1.7;
	color: #333333;
}
p, .BodyText {
	margin: 0 0 20px;
}
p.BodyTextLarge,
.BodyTextLarge {
	font-size: 18px;
	font-weight: 400;
	color: #666666;
	
	line-height: 1.5;
	margin: 0 0 20px;
}
p.InfoText,
.InfoText {
	font-size: 12px;
	font-weight: 400;
	color: #666666;
	
	line-height: 26px;
	margin: 0 0 20px;
}
a {
	color: #263A9A;
}
a:hover, .hover {	
	color: #1135E5;
}
.inlineLink {
	color: #263A9A;
}

a.DBAButton, .DBAButton {
	background: #F7CF7E;
	border: 2px solid #FFC54D;
	font-weight: 700;
	font-size: 14px;
	padding: 12px 25px;
	line-height: 1.5;
	text-align: center;
	text-transform: uppercase;
	color: #000000;
	-moz-transition: all ease 0.5s;
	-ms-transition: all ease 0.5s;
	-o-transition: all ease 0.5s;
	-webkit-transition: all ease 0.5s;
	transition: all ease 0.5s;
	position: relative;
	display: inline-block;
	vertical-align: middle;
	font-family: "Montserrat", sans-serif;
}
a.DBAButton:hover, .DBAButton:hover,
a.DBAButton:focus, .DBAButton:focus {
	background: #FFC54D;
	text-decoration: none;
	border-color: #EFB337;
}
a.TextButton, .TextButton {
	font-weight: 600;
	font-size: 16px;
	color: #000000;
	-moz-transition: all ease 0.5s;
	-ms-transition: all ease 0.5s;
	-o-transition: all ease 0.5s;
	-webkit-transition: all ease 0.5s;
	transition: all ease 0.5s;
	border: 0;
	position: relative;
	display: inline-block;
	vertical-align: middle;
	padding: 0px 0px 0 25px;
	font-family: "Montserrat", sans-serif;
	background: transparent;
}
a.TextButton:hover, .TextButton:focus{
	text-decoration: none;
}
a.TextButton:before, .TextButton:before {
	content: "\f061";
	display: inline-block;
	font-family:"Font Awesome 6 Free";
	font-weight: 900;
	font-size: inherit;
	text-rendering: auto;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	display: inline-flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	left: 0;
	top: 0;
	text-align: center;
	-moz-transition: all ease 0.5s;
	-ms-transition: all ease 0.5s;
	-o-transition: all ease 0.5s;
	-webkit-transition: all ease 0.5s;
	transition: all ease 0.5s;
	color: #A08B60;
}
.TextButton:hover {
    color: #263A9A;
}
.TextButton:hover:before {
    color: #263A9A;
}
a.BlueButton, .BlueButton {
    height: 58px;
    background: #263A9A;
    font-weight: 700;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    -moz-transition: all ease 0.5s;
    -ms-transition: all ease 0.5s;
    -o-transition: all ease 0.5s;
    -webkit-transition: all ease 0.5s;
    transition: all ease 0.5s;
    border: 0;
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    vertical-align: middle;
    padding: 12px 30px;
    font-family: "Montserrat", sans-serif;
}
a.BlueButton:hover, .BlueButton:hover, a.BlueButton:focus, .BlueButton:focus {background: #cf9d5d;text-decoration: none;}


.xsVisible {
	display: none !important;
}
.xsHidden {
	display: block !important;
}
.home.templateHolder input, header input, footer input {
	-moz-border-radius: 0;
	-ms-border-radius: 0;
	-o-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	-moz-appearance: none;
	-ms-appearance: none;
	-o-appearance: none;
	-webkit-appearance: none;
	-moz-box-shadow: none;
	-ms-box-shadow: none;
	-o-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}
.home.templateHolder input:focus, header input:focus, footer input:focus {
	-moz-box-shadow: none;
	-ms-box-shadow: none;
	-o-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

.printFooter,
.printHeader {
	display: none;
}
/************Header**********/
.header {
	/*position: relative;*/
	transition: all 0.5s ease 0s;
	left: 0;
	top: 0;
	width: 100%;
	z-index: 2;
	background: #ffffff;
	position: fixed;
    top: 0;
    width: 100%;
    left: 0px;
    right: 0px;
}
.headerspace {
	width: 100%;
	height: 118px;
}
.header a {
	text-decoration: none;
	display: inline-block;
}
.logo img {
    width: 360px;
}
.top-header .headTop-right {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 15px;
    margin: 0;
    width: 100%;
}
.navbar.navigation .nav>ul>li.dropdown>a {
}

.navbar.navigation .nav>ul>li.dropdown>a:after {
    content: "";
    position: absolute;
    top: 28px;
    font-family: "Font Awesome 6 Free";
    right: 6px;
    width: 15px;
    height: 15px;
    color: #727272;
    font-size: 15px;
    -moz-transition: all ease 0.5s;
    -ms-transition: all ease 0.5s;
    -o-transition: all ease 0.5s;
    -webkit-transition: all ease 0.5s;
    transition: all ease 0.5s;
    font-weight: 900;
}
.bottom-header .container-fluid {
    padding: 0;
    max-width: 1920px;
}
#header.fixed-header.shrink + .clearfix {
    margin-bottom: 120px;
}

/***********Top Header*******/
.search-box { display: none; }
.logo {
	display: inline-block;
	vertical-align: middle;
	padding: 10px 20px 10px 25px;
	align-self: center;
}
.top-header .top-headerFrame {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0;
}
.headTop-left { margin: 0; }
ul.toplist {
	text-align: right;
	list-style: none;
	margin: 0;
	display: inline-flex;
	gap: 0;
	justify-content: space-between;
}
ul.toplist>li>a>i {
    color: #A08B60;
}
ul.toplist>li>a>i {
    margin-right: 5px;
}

ul.toplist>li {
    border-right: 1px solid #2B2F3233;
    padding: 10px 30px 10px 10px;
}
ul.toplist li {
	display: inline-block;
	vertical-align: middle;
	padding: 0 0px;
}
ul.toplist li:first-child { border-left:0; }

ul.toplist>li>a>i {
    margin-right: 5px;
}

ul.toplist>li {
    border-right: 1px solid #2B2F3233;
    padding: 12px 32px;
}
ul.toplist li:last-child {padding-right: 0;border-right: .0;}
ul.toplist>li>a {
	font-size: 15px;
	color: #000000;
	display: block;
	text-transform: uppercase;
	font-weight: 700;
	padding: 0 0;
	font-family: "Merriweather", serif;
}
ul.toplist>li>a>i {
    margin-right: 5px;
}
ul.toplist li a:hover,
ul.toplist li a:focus {
    color:#A08B60;
}
.btn-topbox {width: 148px;text-align: right;float: right;margin-right: 20px;}
.btn-topbox .DBAButton {
	width: 100%;
	font-size: 15px;
	height: 40px;
	line-height: 30px;
	padding: 5px;
}
.btn-topbox .DBAButton span {
	width: 100%;
	display: block;
}
.btn-topbox .DBAButton i {
    font-size: 120%;
    vertical-align: middle;
    margin-right: 10px;
}
.top-btn-wrap .DBAButton:before {content: "";background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAVCAMAAAB1/u6nAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAblQTFRFAAAA////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////dWJVLAAAAJN0Uk5TAA1PkcDc3cSVVREFcN7/+fjleQsksl8uGBcsWqv+zC8oxzkwvegxB7qKtC2p2g58y1v3u5Ma8vw4HvA1M/Ql+ydnqm25onecpIuJRriIjyL92a+adRT20T0GzUQfrFII4T5ym4a+A4yH+vUZIO80khZWnQwC0plUEqFINuTqGzvuBINF130QDzJ62OCO7Zez8bZ05s5EhAAAAWpJREFUeJxNkfc/AkAYxl9UZg8RychMKkQkO0T2CpW9MorsvffI9hd7rz59uB+e+95z9z73ufeIQiMqOkYilcXGxSfQ30hMkgNITmFRpKZFXKUESM9QZaqzsnNyockLu/lyFBRGjhQVQ1siQKdBKReq9AaD3siBZSivYNuEyiiiKjMHw5zBfjVqiCy11jqi+gboG5tM1uYWDjXbWqkN7VxiR4cI7ISDtQsm6kY2Uw96hZ2EONY+9JMG4uIBDArbgRjWoeERkkMZCnEKexRjrOMuBbmhYvJANsHTJKZYp61SmsEs09w8PER5VoV4+gIktIglUe7F8go54RW8ijVS+vytjOrm3HVdg22D0RLAJtEWDNs7uwXQ7u0fYPLw6PgEObxZIYWW3y07JTpzM5zj4lJEXV3D5jXehLt564PrLtzMunvIHpoeV/afnl+CeL2LNPn4zQ8EgvfvgO/D8u/bpu2f0q9a+fdPS3j9C80iPXj9lc/WAAAAAElFTkSuQmCC');width: 20px;height: 20px;display: inline-block;background-size: contain;vertical-align: text-bottom;margin-right: 5px;}
.searchbar {
	border: 2px solid #b6c8ce;
	background: #fff;
	padding: 0 10px 0 0;
}
.searchbar form {
	margin: 0;
}
.searchbar .input-append {
	margin: 0;
	height: 100%;
	width: 100%;
}
.searchbar .input-append .btn {
	background: transparent;
	border: 0;
	box-shadow: none;
	height: 40px;
	padding: 0 10px;
	font-size: 16px;
}
.searchbar .input-append input {
	border: 0;
	height: 26px;
	padding: 0 5px;
	min-height: 40px;
	width: calc(100% - 50px);
	font-size: 20px;
}
.btn-x {
    position: absolute;
    color: #000;
    display: inline-block;
    font-size: 22px;
    z-index: 1;
    line-height: 1;
    padding: 8px;
}

.btn-topbox .DBAButton {
    height: 119px;
    margin-bottom: -110px;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
    width: 148px;
}
.BlueButton span {
    display: block;
    font-size: 28px;
    font-weight: 700;
    font-family: "Merriweather", serif;
}
.top-header .container {
    position: relative;
}
.top-header {
	background: #fff;
}
.bottom-header {
}
.searchBtnFn {
    align-self: center;
    position: static;
}
.searchBtnFn>a {
    color: #000000;
    padding: 6px;
}

.searchBtnFn .searchbar.dropdown-menu {
	left: auto;
	right: 170px;
	top: 0;
	width: 100%;
	padding: 16px 20px;
	border-style: none !important;
	max-width: calc(100% - 175px);
	box-shadow: none;
	margin: 0;
}

#header.fixed-header.shrink {
	position: fixed;
	top: 0;
	width: 100%;
	left: 0px;
	right: 0px;
	transition: all 0.5s ease 0s;
	background: #fff;
	z-index: 99;
}
.banner-content-bottom.fixed {
	position: fixed;
	top: 132px;
	left: 0;
	width: 100%;
	z-index: 2;
	height: 55px;
}
.banner-content-bottom.fixed div {
	background: rgb(114 143 186 / 95%);
	justify-content: center;
}
.banner-content-bottom.fixed div a { width: 32%; }
.banner-content-bottom.fixed div a span img {
	width: 22px;
}
.banner-content-bottom.fixed div a span:nth-child(2) { display: none; }
.banner-content-bottom.fixed div a span {
	padding: 0px 30px;
	height: 40px;
	line-height: 40px;
}
.banner-content-bottom.fixed div a span:first-child { padding: 0 10px; }
.banner-content-bottom.fixed div a span img:nth-child(2),
.banner-content-bottom.fixed div a:hover span img:nth-child(1),
.banner-content-bottom.fixed div a:focus span img:nth-child(1) {
	width: auto;
}
.banner-content-bottom.fixed div a:hover span img:nth-child(2),
.banner-content-bottom.fixed div a:focus span img:nth-child(2) {
	width: 22px;
}
.innerBanner .item {
    margin: 0 auto;
    max-width: 1920px;
    position: relative;
	z-index: 0;
    background: #F6F6F6;
}

/*******navigation******/

.navigation .navbar-inner {
	padding: 0;
	border: none;
	-moz-box-shadow: none;
	-ms-box-shadow: none;
	-o-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
	background: transparent;
	min-height: inherit;
}
.navbar.navigation {
	margin-bottom: 0px;
	flex: 0 0 calc(100% - 400px);
	max-width: calc(100% - 400px);
	padding-right: 0;
	border-left: 1px solid #DCDDDE;
}
.headTop-right.secondboxUL {
	margin: 0;
	display: flex;
	justify-content: space-between;
	width: 100%;
}
.navigation .nav {
	margin: 0px;
	border-radius: 0px;
	display: inline-flex;
	vertical-align: middle;
	justify-content: flex-end;
	float: left;
	width: 100%;
	padding-right: 175px;
	padding-left: 15px;
	border-top: 1px solid #DCDDDE;
}
.navigation .nav ul {
	margin: 0px;
	border-radius: 0px;
}
.navigation .nav >ul {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 0;
	list-style: none;
	width: 100%;
	
}
.navbar.navigation .nav>ul>li {
	text-align: center;
	position: relative;
	padding: 0;
	display: inline-block;
    vertical-align: middle;
}
.navbar.navigation .nav ul > li>a {
	display: block;
	-moz-text-shadow: none;
	-ms-text-shadow: none;
	-o-text-shadow: none;
	-webkit-text-shadow: none;
	text-shadow: none;
	-moz-transition: all ease 0.5s;
	-ms-transition: all ease 0.5s;
	-o-transition: all ease 0.5s;
	-webkit-transition: all ease 0.5s;
	transition: all ease 0.5s;
	-moz-border-radius: 0;
	-ms-border-radius: 0;
	-o-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	font-size: 14px;
	line-height: 20px;
	color: #000000;
	font-weight: 600;
	padding: 27px 17px;
	font-family: "Montserrat", sans-serif;
}
.navbar.navigation .nav ul > li>a:hover, .navbar.navigation .nav ul > li>a:focus, .navbar.navigation .nav ul > li:hover> a {
	background: transparent;
	color: #263A9A;
}
.navbar.navigation .nav ul > li a > img {
	margin-left: 5px; position: relative; top:-2px;
}

.navbar.navigation .nav ul li.dropdown>ul.dropdown-menu:before {
    content: "";
    /* background: #f2f2f2; */
    /* clip-path: polygon(50% 0%, 0 100%, 100% 100%); */
    width: 30px;
    height: 15px;
    display: inline-block;
    position: absolute;
    left: 20px;
    top: -16px;
}

/* .navbar.navigation .nav ul li.dropdown > ul > li > ul >li:hover > a {
	text-decoration: none;
}
.navbar.navigation .nav ul li.dropdown > ul > li > ul > li > ul >li:hover > a {
	text-decoration: none;
} */
.navbar.navigation .nav ul>li>.dropdown-menu::after,
.navbar.navigation .nav ul>li>.dropdown-menu::before {
	display: none;
}
.navigation .dropdown-menu {
	padding: 0px 15px;
	width: 190px;
	background: #ffffff;
	border-top: 6px solid #263A9A;
}
.navbar.navigation .nav ul li.dropdown ul li a {
	min-height: inherit;
	padding: 13px 0px;
	font-weight: 600;
	text-align: left;
	font-size: 14px;
	line-height: 20px;
	white-space: normal;
}
.navbar.navigation .nav ul li.dropdown ul li>a:hover, .navbar.navigation .nav ul li.dropdown ul li a:focus, .navbar.navigation .nav ul li.dropdown ul li.active a {
	color: #263A9A;
	background: transparent;
	text-decoration: underline;
}
.navbar.navigation .nav ul li.dropdown ul li:hover > a {
	color: #263A9A;
}
.dropdown-submenu>a:after {
display: none;	
}
.navMenu>ul>li.dropdown .dropdownArrow {
	display: none;
}
.navMenu>ul>li {
	position: relative;
	
}
.navMenu>ul>li>ul>li, .navMenu>ul>li>ul>li>ul>li  {
	border-bottom: 1px solid #EDEEEE;
}
.navMenu>ul>li>ul>li:last-child, 
.navMenu>ul>li>ul>li>ul>li:last-child {
    border-style: none !important;
}
.dropdownArrow:after {
	content: "\f054";
	position: absolute;
	font-family: "Font Awesome 6 Free";
	top: 17px;
	right: 0;
	width: 15px;
	height: 15px;
	color: #000000;
	font-size: 13px;
	-moz-transition: all ease 0.5s;
	-ms-transition: all ease 0.5s;
	-o-transition: all ease 0.5s;
	-webkit-transition: all ease 0.5s;
	transition: all ease 0.5s;
	font-weight: 900;
}
.navMenu > ul > li.dropdown ul li .dropdownArrow {
	display: block;
	position: absolute;
	top: -3px;
	right: 0;
}
.menuDiv .headTop-right {
	display: none;
}
.search-icon { display: inline-block; vertical-align: middle; margin-right: 20px; }
.searchbar-top {
	display: none;
	position: absolute;
	width: 100%;
	left: 0;
	top: 120px;
	background: #fff;
	padding: 20px 50px;
}
.home input.search-bar {
	height: 50px;
	width: calc(100% - 200px);
	margin: 0 10px 0 0;
	padding: 0 20px;
	font-size: 16px;
}
.menuDiv .LNFButton {
	margin: 0;
	display: inline-block;
	vertical-align: middle;
}


/*****Banner *****/
.slider {
	overflow: hidden;
}
.slider .owl-carousel .owl-stage .owl-item {
    padding-bottom: 60px;
}

.slider .owl-carousel .owl-stage,
.slider .owl-carousel .owl-stage .owl-item,
.slider .owl-carousel .owl-stage .owl-item .item {
	height: 100%;
	/* background: #005187; */
}
.slider .owl-carousel .owl-stage .owl-item .item img {
	width: 100%;
	height: 100%;
	/* max-width: inherit; */
	object-fit: cover;
}
.slider .homeSlider.owl-carousel .owl-nav button.owl-prev, .slider .homeSlider.owl-carousel .owl-nav .owl-next {
    margin-top: -100px;
}

.event-slider.sliderEnabled:after {
    content: "";
    display: block;
    background: linear-gradient(90.08deg, rgba(255, 255, 255, 0) 0.07%, #FFFFFF 68.89%);
    position: absolute;
    width: 40%;
    height: 100%;
    top: 0;
    right: 0;
    z-index: 1;
}

.owl-carousel .owl-nav button.owl-prev, 
.owl-carousel .owl-nav .owl-next {
    position: absolute;
    transform: translateY(-50%);
    top: 50% !important;
    border: 2px solid #ffffff !important;
    color: #fff !important;
    background: #4040404D !important;
    width: 50px;
    height: 50px;
    border-radius: 50% !important;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    line-height: 1;
    z-index: 5;
}

.owl-carousel .owl-nav button.owl-prev {
    left: 20px;
}

.owl-carousel .owl-nav button.owl-next {
    right: 20px;
}
.slider .homeSlider.owl-carousel .owl-nav button.owl-prev {
    left: 50px;
}

.slider .homeSlider.owl-carousel .owl-nav button.owl-next {
    right: 50px;
}

.owl-carousel .owl-nav button.owl-prev:hover, 
.owl-carousel .owl-nav .owl-next:hover {
    background: rgb(255 255 255 / 80%) !important;
    color: #A08B60 !important;
    border-color: #A08B60 !important;
}
.owl-carousel .owl-nav button.owl-prev.disabled,
.owl-carousel .owl-nav button.owl-next.disabled {
    opacity: 0;
}

.top-hero-banner-wrap {
	position: relative;
	background: linear-gradient(to bottom,  rgba(163,163,163,1) 0%,rgba(242,242,242,1) 54%,rgba(169,169,169,1) 99%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	margin-bottom: -7%;
	z-index: -1;
}
.slider .owl-carousel .owl-stage .owl-item .item {
    min-height: 430px;
    position: relative;
    padding-bottom: 60px;
}
.slider .owl-carousel .owl-stage {
    padding-bottom: 100px;
}
.carousel-caption {
	position: absolute;
	top: auto;
	left: auto;
	bottom: -50px;
	right: 0;
	padding: 40px 40px;
	background: #A08B60;
	width: 100%;
	transform: none;
	list-style: none;
	max-width: 60%;
	z-index: 2;
	margin: 0;
}
.carousel-caption .TitleText {
	color: #ffffff;
	margin: 0 0 10px;
}
.carousel-caption li h1 small {
	font-size: 35px;
	color: #fff;
	font-weight: normal;
	display: block;
	line-height: 1.2;
	margin:10px 0 20px 0;
}
.carousel-caption li p {
	font-size: 15px;
	line-height: 1.4;
	margin-bottom: 25px;
	font-weight: 500;
	font-family: "Montserrat", sans-serif;
}
.carousel-caption li p:last-child {
    margin: 0;
}
.carousel-caption .SubHeadline {
    color: #ffffff;
}
.carousel-caption  .BlueButton {
    position: absolute;
    bottom: -56px;
    left: 0;
    z-index: 1;
}
.BulletList ul ul, .BulletList ul ol {
    margin-top: 15px;
}
.homeSlider.owl-theme .owl-dots .owl-dot span {
    width: 75px;
    height: 4px;
    border-style: none;
    background: #A08B60;
    border-radius: 0px;
}

.homeSlider.owl-theme .owl-dots .owl-dot.active span {
    background: #263A9A;
}
.homeSlider .owl-dots {
	position: absolute;
	bottom: 120px;
	left: 0;
	right: 0;
	margin: 0px 0 0;
	text-align: center;
	width: 40%;
}
.homeSlider .owl-dots .owl-dot {
	background: transparent;
	border: 0;
	padding: 0 7px !important;
}
.homeSlider .item>img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 1;
	-webkit-mask-image: linear-gradient(to right, black 20%, transparent 100%);
	mask-image: linear-gradient(to right, black 20%, transparent 100%);
	-webkit-mask-repeat: no-repeat;
  	mask-repeat: no-repeat;
}
.slider .homeSlider {
    /* max-width: 1440px; */
    max-width: 1920px;
    margin: 0 auto;
}
img.top-hero-banner {
    width: 100%;
    position: relative;
    z-index: -1;
    opacity: 0.8;
}

.owl-theme .owl-dots .owl-dot span {
	width: 14px;
	height: 14px;
	margin: 0 0px;
	background: transparent;
	display: block;
	-webkit-backface-visibility: visible;
	-webkit-transition: opacity 200ms ease;
	-moz-transition: opacity 200ms ease;
	-ms-transition: opacity 200ms ease;
	-o-transition: opacity 200ms ease;
	transition: opacity 200ms ease;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
	border: 2px solid #fff;
}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
	background: #cdb687;
}
/********Content*******/
.dbamission-box {
	padding: 0px 0 40px;
}
.dbamission-box .container {
    max-width: 1140px;
}
.dbamission-box p {
	margin: 0;
	font-size: 18px;
	line-height: 1.5;
	color: #ffffff;
}
.event-box {border: 1px solid #DBDBDB;padding: 20px 20px 15px;margin: 10px 7px;}
.event-box a {
	text-decoration: none;
	color: #000000;
}
.event-box:hover {
    border-color: #263A9A;
    outline: 3px solid #263A9A;
    outline-offset: 5px;
}
.event-box  .event-date {
	display: flex;
	justify-content: space-between;
}
.event-box  .event-date .eb-date span {
	font-family: Merriweather;
	font-size: 36px;
	font-weight: 700;
	line-height: 1;
	text-align: center;
	
	display: block;
}
.event-box  .event-date .eb-date {
	height: auto;
	font-size: 17px;
	font-weight: 800;
	line-height: 1.2;
	color: #A08B60;
	font-family: "Montserrat", sans-serif;
}
.event-box .eb-icon {
	width: 40px;
	height: 40px;
	position: relative;
	margin-left: 20%;
}
.event-box .event-content {padding: 15px 0 0;}
.event-box .event-content h6 {
	margin:0;
	letter-spacing: 0;
	font-family: "Merriweather", serif;
	font-size: 18px;
	font-weight: 400;
	line-height: 1.2;
	text-align: left;
	color: #000000;
	min-height: 65px;
}
.event-box .marker-icon {
	position: relative;
}
.event-box .marker-icon-2 {
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
}
.event-box:hover .marker-icon {
	opacity: 0;
}
.event-box:hover .marker-icon-2 {
	opacity: 1;
}
.event-box .event-info {
	display: flex;
	justify-content: space-between;
	border-top: 1px solid #A08B60;
	padding-top: 15px;
	margin-top: 15px;
}
.event-box .event-content h5 {
	font-size: 18px;
	font-weight: normal;
	line-height: 1.5;
	margin-top: 10px;
}
.event-box .event-info span {
    font-family: "Montserrat", sans-serif;
    font-size: 13px;
    font-weight: 700;
    line-height: 14.95px;
    letter-spacing: 0.08em;
    text-align: left;
    text-decoration-skip-ink: none;
}
.event-box .event-content h5 a,
.event-box .event-content h6 a {
	text-decoration: none;
}
.event-box:hover h6 {
    color: #263A9A;
    text-decoration: underline;
}

.event-box:hover .event-info span {
    color: #263A9A;
}
.event-box:hover .event-date .eb-date {
    color: #263A9A;
}
.event-slider.owl-carousel .owl-nav button.owl-prev {
    left: -30px;
    opacity: 0;
}
.event-slider.owl-carousel .owl-nav button.owl-next {
    background: transparent;
    border-color: #A08B60;
    color: #A08B60;
}
.btn-center {
	text-align: center;
	margin:40px auto 0 auto;
}
.eventbox-col .inner-space-30 {
    padding: 15px 30px;
    border-top: 1px solid #DCDDDE;
    border-bottom: 1px solid #DCDDDE;
}
.eventbox-col .inner-space-30 p {
	font-size: 14px;
}
.adv-box h2 {
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    color: #9E9E9E;
    margin: 12px 0;
}

.adv-box.eventbox-col {
    text-align: center;
    padding: 0px 30px 30px;
}

.infoBox {
	background: #ededf4;
	padding: 70px;
	position: relative;
	z-index: 1;
}
.infoBox::before {
	content: '';
	position: absolute;
	height: 732px;
	width: 100%;
	bottom: 0;
	left: 0;
	background-position: center bottom;
	background-size: cover;
}
.infoBox .bg-img {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -1;
	top: 0;
	left: 0;
	object-fit: cover;
	opacity: 0.5;
}
.infoBox h4.SectionHeader span {
	background: #ededf4;
}
.infoBox p {
	padding: 0 80px; position: relative; margin-bottom: 40px;
}
.blog-row {
	position: relative;
	padding: 0 200px;
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}
.blog-date {
	width: 85px;
	height: 85px;
	background: #3b75b1;
	text-align: center;
	padding: 10px;
	margin-right: 30px;
	color: #fff;
	font-size: 26px;
	line-height: 1.1;
}
.blog-date strong { font-weight: 500; font-size: 30px; }
.blog-col {
	text-align: left;
	width: calc(100% - 115px);
}
.blog-col span {
  font-size: 16px;
  font-weight: 700;
  color: #005187;
  text-transform: uppercase;
  
}
.blog-col h5 {
	font-size: 20px;
	color: #002b55;
	line-height: 1.2;
	margin: 5px 0 0;
	font-weight: 400;
	
}


/*Sponsor CSS*/
.header-bar .HeaderText {
	/* background: #1b2b54; */
	/* color: #fff; */
	/* padding: 5px 0; */
	/* margin-bottom: 50px; */
}
.spacing { padding: 45px 0; }
.pdt-0 {
	padding-top: 0 !important;
}
.pdb-0 {
	padding-bottom: 0 !important;
}
.HeaderText .HeaderTextCaps { display: block; margin-bottom: 15px; }
.button-box { text-align: center;  margin-top: 50px; }
.button-box .LNFButton { width: 188px; }

.sponsorSliderDiv ul { margin: 0; padding: 0; list-style: none; }

.sponsorSlider .item {
	text-align: center;
}
.sponsorSlider .item ul {
	vertical-align: top;
	text-align: center;
	width: 100%;
	margin: 0;
	justify-content: space-around;
	display: flex;
	align-items: center;
}
.sponsorSlider .item ul > li {
	display: inline-block;
	vertical-align: top;
	padding: 0 20px;
}
.sponsorSlider .owl-nav {
	margin-top: 0;
	line-height: 0;
}
.sponsorSlider .owl-stage {
	margin: 0 auto;
}
.sponsorSlider .item img {
	width: auto;
	margin: 0 auto;
	max-height: 80px;
	object-fit: contain;
}


/****************/
.subfooter {
	background: #cdb996;
	position: relative;
}
.subfooter .container {position: relative;}
.logo-img {
	position: absolute;
	left: -30px;
	top: 0px;
	-webkit-transform: translateX(-100%);
	transform: translateX(-100%);
}
.subfooter h4 {
	font-size: 23px;
	margin: 0 0 10px 0;
	color: #ffffff;
	font-family: "Merriweather", serif;
	line-height: 1.4;
}
.subfooter-content p {
	font-size: 18px;
	line-height: 22px;
	width: calc(100% - 200px);
}
.subfooter-content {
	position: relative;
}
.button-block-right-line {
	position: absolute;
	right: 0;
	top: auto;
	bottom: 20px;
}
.button-block-right-line .DBAButton {
	width: 155px;
}
.subfooter-left {
	padding: 20px 0px;
}
.subfooter-right {
	border-left: 2px solid #fff;
	padding: 20px 0 20px 30px;
}
.input-element .form-control {
	width: 215px;
	height: 50px;
	border: 1px solid #ffffff;
	font-size: 20px;
	color: #9fa2a5;
	padding: 0 12px;
}
.input-element .form-control:focus {
	outline: none;border: 1px solid #1b2b54;
}
.loginDiv .DBAButton {
	height: 50px;
	padding: 0 10px;
}
.input-element a {
	font-size: 16px;
	color: #333333;
}
/*********Footer********/

.footer {
	background: #222655;
	position: relative;
}
.footerBG {
    position: absolute;
    right: 0;
    bottom: 0;
    top: 0;
    left: 0;
    z-index: 1;
    max-width: 100%;
    opacity: 0.1;
}
.footerBG img {
	height: 100%;
	object-position: left top;
	width: 100%;
	object-fit: cover;
}
.footerTop {
	position: relative;
	z-index: 2;
	overflow: hidden;
	padding: 0 0 40px;
}

.footer .footCol2 {
	margin-left: 30px;
}
.footerLogo img {
    mix-blend-mode: color-dodge;
}

.footerLogo {
    background: #222655;
    display: block;
}
.homeCols p {
	color: #fff;
	font-size: 18px;
	margin: 30px 0 25px 0;
}
.footCol1 .link {
	color: #b6c8ce;
	font-size: 18px;
}
.homeCols .HeaderTextSmall {
	color: #fff;
	font-size: 15px;
	font-weight: 800;
	text-transform: uppercase;
	line-height: 100%;
	margin: 0 0 30px 0;
	font-family: "Montserrat", sans-serif;
}
.homeCols ul {
	margin:0;
	padding: 0;
	list-style: none;
	min-width: 30%;
}
.homeCols ul li a {
	font-size: inherit;
	font-weight: inherit;
	color: #fff;
	display: block;
	margin-bottom: 20px;
	font-family: "Montserrat", sans-serif;
}
.homeCols ul li a:hover, .homeCols ul li a:focus {color: #A08B60;text-decoration: underline;}
.list-inline {
	display: flex;
}
.list-inline ul:not(:last-child) {
	margin-right: 20px;
}
.list-inline ul li {
	position: relative;
	padding-left: 20px;
}
.list-inline ul li a {
	color: #fff;
	font-size: 14px;
	font-weight: normal;
}
.list-inline ul li a:hover,
.list-inline ul li a:focus {
	text-decoration: underline;
	color: #fff;
}
.list-inline ul li:before {
	content: "\f105";
	position: absolute;
	font-family: "Font Awesome 6 Free";
	font-weight: 900;
	left:0;
	color: #c8b18b;
	font-size: 14px;
}
.footCol3 ul li {
	display: flex;
	align-items: start;
	margin-bottom: 12px;
}
.footCol3 ul li span {
	color: #c8b18b;
	width: 40px;
	text-align: left;
	font-size: 20px;
}
.footCol3 ul li p, .footCol3 ul li p a {
	font-size: 14px;
	color: #fff;
	margin: 0;
	width: calc(100% - 40px);
	line-height: 20px;
}
.footCol3 ul li p a:hover,
.footCol3 ul li p a:focus {
	color: #fff;
	text-decoration: underline;
}
.footerTop .footCol1 {
    display: flex;
    gap: 25px;
}
.footerTop .social-links ul li a {
    color: #ffffff;
    font-size: 22px;
}
.footerTop .SubHeadline {color: #ffffff;margin: 15px 0 30px;}
.left-icon-list {
	list-style: none;
	padding: 0;
	margin: 0;
}
.left-icon-list li {
	position: relative;
	padding-left: 25px;
	font-family: "Montserrat", sans-serif;
	font-size: 18px;
	font-weight: 500;
}
.left-icon-list li i {
	position: absolute;
	left: 0;
	top: 0;
	color: #A08B60;
}
.footer .footCol1 li:nth-child(2), .footer .footCol1 li:nth-child(3) {
    max-width: 100%;
    display: inline-block;
    margin-right: 25px;
}
.footer .copyrightList {
	padding-left: 180px;
	margin: 0;
}
.copyrightList ul li a:hover {
    color: #ffffff;
}
.homeCols.footCol1 , .homeCols.footCol2 {
    padding: 40px 0 0;
    /* position: relative; */
}
.homeCols.footCol2:after {
    content: "";
    position: absolute;
    width: 100%;
    background: #263A9A;
    height: 100%;
    top: 0;
    left: 58%;
    z-index: -1;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 70px 100%);
}
/*copyright*/
.copyright { position: relative; z-index: 1; }
.copyright-in {
	padding: 20px 0 10px 0;
}
.copyright-in .social-links ul {
    justify-content: end;
}

.copyright-in  .social-links ul li:first-child {
    color: #ffffff;
    text-transform: none;
    font-size: 14px;
}
.social-links ul {
	text-align: right;
	list-style: none;
	margin: 0;
	display: flex;
	align-content: center;
}
.social-links ul li {
	color: #fff;
	font-size: 15px;
	margin-left: 15px;
}
.social-links ul li a {
	color: #000000;
	font-size: 16px;
}
.social-links ul li:first-child {
    color: #607c8c;
    margin-left: 0;
    text-transform: uppercase;
}
.copyrightList ul {
	list-style: none;
	margin:0;
	display: flex;
	align-items: center;
}
.copyrightList ul li,
.copyrightList ul li a {
	color: #a7a8bb;
	font-family: "Montserrat", sans-serif;
	font-size: 14px;
	font-weight: 500;
	text-align: left;
}
.copyrightList ul li {
	position: relative;
	margin:0 10px;
	/* opacity: 0.6; */
}
.copyrightList ul li:first-child { margin-left: 0; }
.copyrightList ul li:first-child {
	padding-left: 0;
}
.copyrightList ul li:not(:last-child):after {
	position: absolute;
	content: '';
	top: 3px;
	right: -10px;
	height: 15px;
	width: 1px;
	background: #a7a8bb;
	border-radius: 0px;
}
.copyright-in li:last-child:after {
	display: none;
}

.copyright a:hover {
	text-decoration: underline;
}
.copyrightList ul li a {
    text-decoration: underline;
}
/******Inner Page*****/

.innerBanner {
	position: relative;
	margin: 0 0 65px;
}
.innerBanner .item > img {width: 54%;opacity: 1;max-height: 275px;object-fit: cover;}

.banner-logo {
    width: 37%;
    height: 100%;
    left: auto;
    opacity: 1;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    background: #ffffff;
    z-index: 2;
    opacity: 0.06;
}
.slider .owl-carousel .owl-stage .owl-item .item .banner-logo img {
    mix-blend-mode: difference !important;
    opacity: 1;
    height: 100%;
    width: 100%;
    /* opacity: 0.06; */
}
.banner.innerBanner {margin-top: 120px;}

.banner.innerBanner .banner-logo img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.banner.innerBanner .banner-logo {
    width: 41%;
    background: #F6F6F6;
    opacity: 1;
}

.banner-content {
	position: absolute;
	top: auto;
	bottom: -65px;
	left: 0;
	width: 50%;
	right: 0;
	background: #A08B60;
	padding: 30px 40px;
	z-index: 2;
}

.banner-content.bg-blue {
    background: #263A9A;
}
.banner.innerBanner .banner-logo:before {
    position: absolute;
    width: 100%;
    height: 100%;
    content: "";
    left: -100%;
    background: linear-gradient(89.49deg, rgba(246, 246, 246, 0) 2.1%, #F6F6F6 66.04%);
    z-index: -1;
}
.banner-content .PageTitle {
	position: relative;
	margin: 0;
}
.PageTitle {
	font-size: 60px;
	line-height: 1.4;
	color: #005187;
	font-family: "Merriweather", serif;
	text-transform: uppercase;
}
.banner-content .SubHeadline {
    color: #ffffff;
    margin: 0 0 5px;
}

.banner-content .TitleText {
    color: #fff;
}

.innerContent .container {
	position: relative;
}
.innerBanner .breadcrumb {
    padding-left: 52%;
    background: transparent;
    position: absolute;
    bottom: 0;
    margin: 0;
    top: auto;
    bottom: -55px;
}

.innerBanner .breadcrumb a, .innerBanner .breadcrumb li {
    font-family: "Montserrat", sans-serif;
    font-size: 13px;
    font-weight: 600;
    line-height: 27px;
    color: #868686;
    text-shadow: none;
}
.innerBanner .breadcrumb a:hover {
    color: #1135E5;
}
.innerBanner .breadcrumb a img {
    vertical-align: middle;
    display: inline-block;
    margin: -3px 2px 0 0;
}
.innerContent {
	display: flex;
	padding-top: 70px;
	background: linear-gradient(181.86deg, #FFFFFF 79.86%, #F7F7F7 100.42%);
}
.innerContent .rightInner {
	background: #fff;
	padding: 0 165px 0 80px;
	margin: 0;
	width: calc(100% - 340px);
}
.innerContent .leftInner {
	 margin: 0;
	 width: 340px;
	 padding-left: 40px;
}
.innerContent .leftInner h3 { margin-bottom: 30px; color: #242424; }
.BulletList ul { margin:0; list-style: none; }
.BulletList ul li {
	position: relative;
	padding-left: 40px;
	margin-bottom: 15px;
	line-height: 1.5;
	color: #005187;
}
.BulletList ul li a {
	font-size: 26px;
}
.BulletList ul li a:hover, .BulletList ul li a:focus {
	color: #2C4B89;
}
.BulletList ul li:before {
	content: '';
	position: absolute;
	width: 25px;
	height: 25px;
	background: url(./../images/external-link.png) no-repeat;
	top: 2px;
	left:0;
}

.accordion .DBAButton {
	margin: 0;
}
.accordion .DBAButton span {
	display: none;
}
.accordion .DBAButton:hover span {
	display: inline-block;
}
.innerImage {
	margin-top: 30px;
}
.rightInner p {
}


.eventbox-row {
	display: flex;
	flex-direction: column;
}
.eventbox-col {
    width:100%;
    margin: 0 0 40px 0;
    border: 1px solid #DCDDDE;
}
.eventbox-col:last-child {
    margin-right: 0px;
}
.eventone {
    background: #c8b18b;
}
.eventone .eventbox-img {
    background: #d09d5d;
}
.eventtwo {
	background: #aaaaaa;
}
.eventtwo .eventbox-img {
	background-color: #666666;
}
.eventthree {
	background: #005187;
}
.eventthree .eventbox-img {
	/* background: #4c85ab; */
}
.eventthree span {
    /* opacity: 0.4; */
    /* mix-blend-mode: hard-light; */
}
.eventtwo span {
    opacity: 0.4;
    mix-blend-mode: difference;
}
.eventone span {
    opacity: 0.5;
    mix-blend-mode: color-burn;
}

.eventbox-img {
    overflow: hidden;
    position: relative;
}
.event-head {
    display: flex;
    gap: 15px;
}
.event-head img {
    width: auto;
}
.event-head .ColumnHeader {
    /* color: #ffffff; */
    margin: 0;
    align-self: center;
}

.eventbox-img .event-head .event-icon {
    padding: 15px;
}

.eventbox-img .event-head .event-icon img {
    width: 31px;
    height: 31px;
    object-fit: contain;
}

.eventbox-img .event-head .event-icon {
    padding: 15px;
    border-right: 1px solid #DCDDDE;
}

.eventbox-img .event-head .event-icon img {
    width: 31px;
    height: 31px;
    object-fit: contain;
}

.sidebar-iconbox {
    border: 1px solid #DCDDDE;
    border-style: solid none;
    display: flex;
    margin-bottom: -1px;
    padding: 10px 20px;
    align-items: center;
    position: relative;
    color: #333333;
    line-height: 18px;
}

.sidebar-iconbox:before {
    content: "\f061";
    position: absolute;
    left: 15px;
    top: 9px;
    font-weight: 900;
    font-family: "Font Awesome 6 Free";
    opacity: 0;
    color: #263A9A;
    font-size: 16px;
}
.sidebar-iconbox:hover:before,
.sidebar-iconbox:focus:before {
	opacity: 1;
}
.sidebar-iconbox:hover,
.sidebar-iconbox:focus {
    text-decoration: none;
    padding-left: 35px;
    color: #263A9A;
}
.sidebar-iconbox .textBox h2 {
	font-size: 14px;
	color: #333333;
	font-weight: 600;
	line-height: 100%;
	margin: 0;
	line-height: 1.4;
	font-family: "Montserrat", sans-serif;
}

.sidebar-iconbox:hover .textBox h2 {
    color: #263A9A;
}
.eventbox-info {
    padding: 0;
}
.eventbox-item {
    text-align: center;
    margin: 10px 0 0 0;
    border-bottom: 1px solid #fff;
}
.eventbox-item:hover .eventbox-item-in { background: #fff; }
.eventbox-item-in {
    padding: 15px 10px;
    margin-bottom: 10px;
}
.eventbox-item ul {
	display: flex;
	justify-content: center;
	list-style: none;
	margin: 0;
}
.eventbox-item ul li {
    font-size: 16px;
    color: #FFFFFF;
    position: relative;
    padding: 0 20px 0 15px;
}
.eventbox-item ul li i { margin-right: 5px; }
.eventbox-item ul li:before {
    content: '|';
    position: absolute;
    height: 25px;
    right:0;
    top:-2px;
}
.eventbox-item ul li:last-child:before {
    display: none;
}
.eventbox-item ul li img { margin-right: 5px; }
.eventbox-item ul li img.hover-img { display: none; }
.eventbox-item:hover ul li img.active-img { display: none; }
.eventbox-item:hover ul li img.hover-img { display: inline-block; }
.eventbox-item p {
    color: #fff;
    font-size: 20px;
    font-weight: 700;
    margin: 10px 0 0 0;
    line-height: 25px;
}
.eventbox-item:hover ul li { color: #1B365D; }
.eventbox-item:hover p { color: #1B365D; }
.eventbox-item.eventbox-item-link {
    border: 0;
    margin: 15px 0;
}
.event-link {
    color: #fff;
    margin: 0 15px;
    text-transform: uppercase;
    font-size: 15px;
    font-weight: bold;
}
.event-link:hover,
.event-link:focus {
    color:#fff;
    text-decoration: underline;
}

.upcomingEvents .flex-row.row-fluid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -45px;
    width: auto;
}

.upcomingEvents .flex-row.row-fluid:before, .upcomingEvents .flex-row.row-fluid:after {
    display: none;
}
.upcomingEvents .SubHeading {
}
.upcomingEvents .span3 {
    padding-left: 80px;
    padding-right: 4%;
    width: 30%;
}
.upcomingEvents .span9 {
    width: 70%;
	margin: 0;
}
blockquote, blockquote.pull-right {
	padding: 20px 0px;
	border: 1px solid #A08B60;
	border-style: solid none;
	color: #333333;
	position: relative;
	font-family: Merriweather;
	font-size: 23px;
	font-style: italic;
	font-weight: 300;
	line-height: 1.5;
	text-align: left;
}
/* blockquote::before {
    content: "";
    position: absolute;
    top: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: 60px;
    background: #fff;
    z-index: 1;
    width: 62px;
    height: 62px;
    padding: 0 15px;
    background-image: url(./../images/Quote.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 65%;
}
blockquote:after {
	position: absolute;
	top: 100%;
	-webkit-transform: translateY(-50%)scale(-1);
	transform: translateY(-50%)scale(-1);
	right:60px;
	background: #fff;
	color: #c8b18b;
	z-index: 1;
	width: 62px;
	height: 62px;
	padding: 0 15px;
	background-image: url(
	./../images/Quote.png);
	background-repeat: no-repeat;
	background-position: center;
	background-size: 65%;
} */
.QuoteAuthor {
	/* font-size: 24px; */
	/* line-height: 100%; */
	/* font-weight: 700; */
	/* color: #c8b18b; */
	/* display: block; */
	/* margin: 25px 0 20px 0; */
	/* text-transform: uppercase; */
}
.bullet-title {
	color: #A08B60;
	text-transform: uppercase;
	margin-bottom: 30px;
}


.rightInner .Highlight p {
	margin: 0 0 30px;
}
.rightInner .Highlight p:last-child {
	margin-bottom: 0;
}

.service-row {
	display: flex;
	flex-wrap: wrap;
	align-items: start;
	justify-content: space-between;
	margin-bottom: 40px;
	gap: 15px;
}
.service-row:last-child { margin:0; }
.service-col {
	display: flex;
	align-items: center;
	max-width: 370px;
	min-width: 350px;
	padding-right: 10px;
}
.service-col:first-child {margin-right: 0;}
.service-col:last-child {margin-left: 0;}
.service-img {
	width: 160px;
	margin-right: 0;
}
.service-info {
	width: calc(100% - 160px);
	padding-left: 30px;
}
.service-info p {
	margin: 10px 0 20px;
}
.service-info .DBAButton {
	width: 190px;
	font-size: 18px;
}
.innerContent .widget .accordion-body {
	margin-bottom: 60px;
}
.innerContent .rightInner .eventFrame .textBox {
	margin-bottom: 20px;
}
.innerContent .rightInner .eventFrame .textBox p {
	margin-bottom: 0px;
}
.innerContent .rightInner .eventFrame .textBox h6 {
	font-size: 12px;
	font-weight: bold;
	color: #202020;
	line-height: 21px;
	margin: 0 0 5px;
}
.innerContent .rightInner .SponsorBox ul {
	margin-left: 0px;
}
.innerpageContent {
	padding: 50px 0px 290px;
}
.list-spacing li {
	line-height: inherit;	
}
.list-spacing li:not(:last-child) {
    margin-bottom: 10px;
}
.quicklink-mobile { display: none; }
.Highlight-title h4 { font-size: 26px;  line-height: 30px; }
.Highlight-title p { color: #767C87; font-weight: 500; margin-bottom: 25px; }
.Highlight .BulletList ul li { font-size: 18px; color: #767C87; }
.feature-content:hover .feature-content-in h4,
.feature-content:hover .feature-content-in p {
	color: #728FBA
}

.quicklinks-submenu {
	display: none;
}
.quicklinks-submenu.show {
	display: block;
}
.innerContent .rightInner.full-rightInner { padding: 0; width: 100%; }
/*template page css*/
.template .content h1, .template .content h2, .template .content h3,
.template .content h4, .template .content h5, .template .content h6 {
	color: #242424; text-decoration: none;
}
.template .content input,
.template .uneditable-input {
	height: auto;
}
.template .navbar .container {
	width: auto;
}
.templatePage {
	margin-top: 260px;
}
.templateBody, .templateBody p { font-size: 14px; line-height: 20px;color: #333; }

.templateBody h1,
.templateBody h2, 
.templateBody h3,
.templateBody h4, 
.templateBody h5,
.templateBody h6 { color: #333; }
/******************/
.Highlight-title p { margin-bottom: 18px; }
.innerContent.hide-box .leftInner {
	display: none;
}

.innerContent .benefit-section,
.innerContent.hide-box .rightInner blockquote,
.innerContent.hide-box .rightInner .hide-item {
	display: none !important;
}
.innerContent.hide-box .rightInner .Highlight {
	display: block;
}
.innerContent.hide-box .rightInner .benefit-section {
	display: block !important;
}
.innerContent.hide-box .rightInner {
	width: 100%; padding: 40px 0 0 0;
}
.innerContent.hide-box .rightInner > div {
	width: 940px; margin: 0 auto;
}
.innerContent.hide-box .rightInner .benefit-section {
	display: block; width: 100%; margin: 0 auto;
}
.Highlight .HeaderText {
	color: #c7c9ce; font-size: 26px;
}

.innerContent.hide-box .container::after {
	display: none;
}
.Highlight {
	background: #F7F7F7;
	padding: 20px 30px;
	margin: 30px 0;
}
.top-btn-wrap {
	display: flex;
}
.txtbtn {
    background: transparent;
    border-color: transparent;
    padding-left: 0;
    margin-right: 0;
    margin-left: 10px;
}
.mobile-menus {
	padding: 15px;
	box-shadow: 0px -4px 10px 0px #00000014;
	margin-top: 30px;
	position: relative;
	top: auto;
	bottom: 0;
	width: 100%;
	top: auto;
	background: #ffffff;
}
.mobile-contact-btn {
	font-size: 20px;
    text-align: center;
    margin: 10px 0;
}
.mobile-contact-btn a {
	color: #242424;
	font-weight: 700;
	margin: 0;
}
.mobile-contact-btn a i {color: #a08b60;}
.mobile-contact-btn a:hover {
	color: #a08b60;
}
.iconbox-sec .flex-row {
	display: flex;
	margin: 0;
	flex-wrap: wrap;
}
.iconbox-sec .flex-row .col-4 {
	padding-left: 0;
	padding-right: 0;
	flex: 0 0 33.33%;
	max-width: 33.33%;
}
.iconbox {
    text-align: center;
    padding: 20px 0;
    border: 1px solid #DBDBDB;
    height: 100%;
}


.iconbox a {
    display: block;
    text-decoration: none;
    width: 100%;
}

.iconbox h2 {
    margin: 15px 0 0;
    text-decoration: none;
    font-family: Merriweather;
    font-size: 18px;
    font-weight: 400;
    line-height: 23.4px;
    text-align: center;
}
.iconbox-sec {
    padding: 30px 0;
}

.iconbox-sec h3 {
    font-size: 42px;
    font-weight: 400;
}

.iconbox-sec h3 {
    font-size: 42px;
    font-weight: 400;
}



.HighlightBox {
	background-color: #A08B60;
	padding: 30px 40px;
	text-align: center;
}
.HighlightBox p {
	color: #ffffff;
	margin: 0 0 15px;
}
.HighlightBox .SubHeadline {
	color: #ffffff;
	text-align: center;
}
.HighlightBox .TextButton:before, .HighlightBox .TextButton {
    color: #ffffff;
}
.HighlightBox h2 {
	font-size: 30px;
	font-weight: 400;
	color: #ffffff;
	margin: 18px 0;
	font-style: italic;
}
ul.mobile-menus-list {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5px !important;
    text-align: center;
}

ul.mobile-menus-list>li {
    width: auto !important;
}

ul.mobile-menus-list li a {
    padding: 0 10px !important;
    font-size: 12px !important;
    text-align: center !important;
}


ul.mobile-menus-list li:nth-child(2) {
    border: 1px solid #aeaeae69;
    border-style: none solid;
}
.submenu {}

.quicklinks-submenu li a {
    color: #333333;
    padding: 5px 0;
    line-height: 1.4;
    display: block;
    font-family: "Montserrat", sans-serif;
}

.quicklinks-submenu {
    border: 1px solid #ffffff;
    margin: -11px 0 0;
    padding: 15px 8px 8px 22px;
    list-style: none;
}

.quicklinks-submenu li {
    list-style: none;
    padding: 0 0 0 18px;
    position: relative;
}

.quicklinks-submenu li:before {
    content: "\f105";
    color: #000000;
    position: absolute;
    left: 0;
    top: 7px;
    font-weight: 900;
    font-family: "Font Awesome 6 Free";
}
.event-head .ColumnHeader:after {
    background: #ffffff;
}
.list-inline ul li>ul {
    display: none;
}
.list-inline ul li.submenu-show>ul {
    display: block;
}




.legal-resources-sec {
	padding: 50px 0 0;
}
.legal-resources-sec .link-wrapper {
	display: flex;
}
.legal-resources-sec .link-wrapper a {
	display: flex;
	flex: 0 0 50%;
	max-width: 50%;
	padding: 36px 10px;
	justify-content: center;
	align-items: center;
	gap: 10px;
	font-family: Merriweather;
	font-size: 18px;
	font-weight: 400;
	line-height: 23.4px;
	text-align: left;
	text-decoration: none;
	color: #000000;
}
.legal-resources-sec .link-wrapper a:first-child {
	border-right: 1px solid #DBDBDB;
}
.no-underline:after {
	display: none !important;
}
.no-underline {
	margin-bottom: 10px;
}
.img-style-1 {
	position: relative;
	z-index: 1;
	padding: 32px 0 0 32px;
	width: 100%;
	max-width: 600px;
	margin: 0 auto;
}
.img-style-1 img {
	width: 100%;
}
.img-style-1 img {box-shadow: -32px -32px 0 #263A9A;}
.legal-resources-sec .btn-center {
	margin-top: 25px;
}
.legal-resources-sec .lrc-content-box {
    padding: 0 10%;
    text-align: center;
    margin: 30px 0;
}
.iconbox-sec .ic-content-wrapper {
	padding: 0 10%;
	text-align: center;
}
.mr-5 {
	margin-right: 5px !important;
}
.legal-resources-sec p {
    padding: 0 5%;
}
.event-slider.sliderEnabled .owl-stage {
    left: 10px;
}
.event-slider .owl-stage{
	margin: 0 auto;
}
.mcText a{
	color: #000 !important;
}
.MCBoxHeader a.DBAButton, .MCBoxHeader .DBAButton,.MCBoxHeader,.MCBoxHeader .mcText,.MCBoxHeader .mcText a {
	color: #0000009e !important;
}
.MCBoxHeader{
	cursor: default;
}
.MCBoxHeader span:hover {
	cursor: pointer !important;
	color: #000 !important;
}
.MCBoxHeader span:hover a{
	cursor: pointer !important;
	color: #000 !important;
}

.MCBoxHeader i:hover{
	cursor: pointer !important;
	color: #000 !important;
}
.mcLoginWrapMob{
	font-family: Montserrat !important;
    font-size: 14px !important;
	line-height: 55px !important;
}
#searchbox{
	margin: 0;
    width: 100%;
    display: flex;
}
#searchbox #s_key_all{
	margin-left: 50px;
}
#searchbox .search-iconin{
	position: absolute;
}
#zoneToolBar form,
#zoneToolBar select {
	margin: 0
}
.hidden{
	display:none !important;
}
.noCaptionOnlyLink{
	background: inherit !important;
	bottom: 0 !important;
}

.allBorder{
	border: 1px solid #DBDBDB;
}
.borderBottom{
	border-bottom: 1px solid #DBDBDB;
	border-top: none !important;
	border-left: none !important;
}
.borderRight{
	border-right: 1px solid #DBDBDB;
	border-top: none !important;
	border-left: none !important;
}
.borderBottomNone{
	border-bottom: none !important;
	border-top: none !important;
	border-left: none !important;
}
.borderRightNone{
	border-right: none !important;
	border-top: none !important;
	border-left: none !important;
}

.zoneFContainerWrap{	
	text-align: center;
    display: flex;
}
.zoneMWrapMob,.zoneNWrapMob{
	position: relative;
}
.zoneMWrapMob > img{
	position: absolute;
    transform: translateY(30%);
    left: 10px;
}
.zoneNWrapMob > img{
	position: absolute;
    transform: translateY(30%);
    left: 10px;
    width: 32px;
}
.span12.removeFlex.rightInner{
	width: 100% !important;
}
.accordion .accordion-group .c-accordion__title.collapsed:after {
    content: "+";
}
.c-accordion__title:after {
    color: #777;
    content: "+";
    font-weight: 400;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}
.accordion .c-accordion__title{
	position: relative;
}
.accordion h3.c-accordion__title{
	cursor: pointer;
}
.accordion .accordion-group .c-accordion__title:not(.collapsed):after {
    content: "-";
}
td input[type="checkbox"] {
  margin: 0;
}