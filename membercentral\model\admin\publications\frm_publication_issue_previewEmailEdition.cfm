<cfsavecontent variable="local.previewEmailJS">
	<cfoutput>
	<script language="javascript">
		let #toScript(local.pubTemplateContent,"pubTemplateContent")#
		let #toScript(local.pubTemplateParsedContentHTML,"pubTemplateParsedContentHTML")#
	
		function sendTestIssueEmailEdition() {
			<cfif local.pubTemplateSuccess>
				var sendTestResult = function(r) {
					$('##btnSendTestIssueEmailEdition').removeAttr('disabled'); 
					if (r.success && r.success.toLowerCase() == 'true') { 
						if(r.outputmessage && r.outputmessage.length > 0) {
							$('##sendTestMessageInfo').html('<div class="alert alert-info mt-2">' + r.outputmessage + '</div>');
						} else {
							$('##sendTestMessageInfo').html('<i class="fa-solid fa-check text-green"></i> Sent');
						}
					}
					else if(r.errmsg && r.errmsg.length > 0) alert(r.errmsg);
					else alert('We were unable to send the test message.');
				};

				var emailRegEx = new RegExp("#application.regEx.email#","gi");
				var emailAddress = $('##previewTestEmailAddr').val();

				if (emailAddress.length == 0 || !(emailRegEx.test(emailAddress))) {
					$('##previewTestEmailAddr').addClass('is-invalid');
				} else {
					$('##previewTestEmailAddr').removeClass('is-invalid');
					$('##btnSendTestIssueEmailEdition').prop('disabled',true);
					var objParams = { publicationID:#local.publicationID#, issueID:#local.issueID#,
						overrideMemberID:#local.strPublicationPreviewMemberID[local.publicationID].memberID#, 
						overrideEmailAddress:emailAddress, templateContent:pubTemplateContent, isTestEmail:1 };
					TS_AJX('ADMINPUBLICATION','doSendEmailIssue',objParams,sendTestResult,sendTestResult,10000,sendTestResult);
				}
			<cfelse>
				alert('We were unable to send the test message.');
			</cfif>
		}
		function writePreviewMessageBody(msg) {
			var msgFrame = frames['previewEmailIssueEditionFrame'].document;
				msgFrame.open();
				msgFrame.write(msg);
				msgFrame.close();

			/* all anchor links in iframe should scroll iframe to target */
			$('##previewEmailIssueEditionFrame').contents().find('body').on('click', 'a', function(e) {scollIframeToAnchor(e,$('##previewEmailIssueEditionFrame').contents())});
			/* all links (except anchor links and anchor names) should open in new window*/
			$('##previewEmailIssueEditionFrame').contents().find('a').not("a[href^='##']").not("a[name]").attr('target','_blank');
		}
		function scollIframeToAnchor(e, iframeContents) {
			if($(e.currentTarget).attr('href').startsWith('##')) {
				var hash = $(e.currentTarget).attr('href').substr(1);
				var targetElement = $(iframeContents).find("a[name='" + hash + "']");
				if ($(targetElement).length) {
					$("html, body").find('##divPreviewEmailEdition').stop().animate({ scrollTop: $(targetElement).offset().top + 150 }, 300, 'swing');
				}
				e.preventDefault();
			}
		}

		$(function() {
			writePreviewMessageBody(pubTemplateParsedContentHTML);
		})
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.previewEmailJS)#">

<cfoutput>
<div id="divPreviewEmailEditionContainer" class="p-3">
	<div class="form-group row">
		<label for="previewEmailEditionRandomMemberName" class="col-sm-2 col-form-label">Member</label>
		<div class="col-sm-auto">
			<input type="text" class="form-control-plaintext" id="previewEmailEditionRandomMemberName" value="#local.strPublicationPreviewMemberID[local.publicationID].memberName#" readonly>
		</div>
		<div class="col text-right">
			<a href="##" class="btn btn-sm btn-success" onclick="loadRandomRecipient();return false;">
				<i class="fa-solid fa-arrows-rotate"></i> Randomly Select Another Recipient
			</a>
		</div>
	</div>
	<div class="form-group row">
		<label for="previewEmailEditionRandomMemberEmail" class="col-sm-2 col-form-label">Email</label>
		<div class="col-sm-10">
			<input type="text" class="form-control-plaintext" id="previewEmailEditionRandomMemberEmail" value="#local.strPublicationPreviewMemberID[local.publicationID].email#" readonly>
		</div>
	</div>
	<div class="card card-box">
		<div class="card-header">
			<div class="form-row w-100">
				<div class="col-sm-12 col-lg-10">
					<div class="input-group input-group-sm">
						<div class="input-group-prepend">
							<div class="input-group-text"><i class="fa-solid fa-envelope"></i></div>
						</div>
						<input type="text" name="previewTestEmailAddr" id="previewTestEmailAddr" class="form-control" value="#session.cfcuser.memberdata.email#" placeholder="Email Address">
						<div class="input-group-append">
							<button type="button" name="btnSendTestIssueEmailEdition" id="btnSendTestIssueEmailEdition" class="btn input-group-text" onclick="sendTestIssueEmailEdition();">
								Send Test Message
							</button>
						</div>
					</div>
				</div>
				<div id="sendTestMessageInfo" class="col-sm-auto mt-1"></div>
			</div>
		</div>
		<div class="card-body p-2">
			<div id="divPreview" style="height:500px;">
				<iframe name="previewEmailIssueEditionFrame" id="previewEmailIssueEditionFrame" class="w-100 h-100 border-0" onload="EmailUtils.resizePreviewMessageBody('previewEmailIssueEditionFrame');"></iframe>
			</div>
		</div>
	</div>
</div>
</cfoutput>